const aPcustomFields = [
    {
      "id": "0yzZup7QBUgBMMOaZstf",
      "name": "Gift Card - Send Email At 2",
      "model": "contact",
      "fieldKey": "contact.gift_card__send_email_at_2",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T12:57:59.619Z",
      "standard": false
    },
    {
      "id": "1VoAGa9PirGQckZfWoZI",
      "name": "Any specific requirements or questions",
      "model": "contact",
      "fieldKey": "contact.any_specific_requirements_or_questions",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:41.499Z",
      "standard": false
    },
    {
      "id": "0AmM2boFK7PmpjqSLke7",
      "name": "Komplikationen bei bisheriger Vollnarkose?",
      "model": "contact",
      "fieldKey": "contact.komplikationen_bei_bisheriger_vollnarkose",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:49.354Z",
      "standard": false
    },
    {
      "id": "1WZD0M3l8GDKskhCaPbi",
      "name": "Gift For",
      "model": "contact",
      "fieldKey": "contact.gift_for",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:43:25.709Z",
      "standard": false
    },
    {
      "id": "1hCSCHhOO39BI0fGKwCJ",
      "name": "Gift Card Message",
      "model": "contact",
      "fieldKey": "contact.gift_card_message",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:47:37.641Z",
      "standard": false
    },
    {
      "id": "18jGswvb3BCYwEjr6dRg",
      "name": "Medikation/Dosierung Selbstangabe:",
      "model": "contact",
      "fieldKey": "contact.medikationdosierung_selbstangabe",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 8650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T14:42:32.844Z",
      "standard": false
    },
    {
      "id": "2Rl3tNKV9E1ZBqgt4y3Y",
      "name": "Wie dürfen wir mit Ihnen Kontakt aufnehmen?",
      "model": "contact",
      "fieldKey": "contact.wie_drfen_wir_mit_ihnen_kontakt_aufnehmen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:50.329Z",
      "standard": false
    },
    {
      "id": "2HAVsYdzXA2fnDkKnQon",
      "name": "Last appointment services",
      "model": "contact",
      "fieldKey": "contact.last_appointment_services",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 1100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T21:14:24.761Z",
      "standard": false
    },
    {
      "id": "30UlbhwqPfXqyw9l6xQZ",
      "name": "Last appointment categories",
      "model": "contact",
      "fieldKey": "contact.last_appointment_categories",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 1300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T21:14:26.716Z",
      "standard": false
    },
    {
      "id": "2tW5ZN6zOHXPK8h4iLRp",
      "name": "IBAN",
      "model": "contact",
      "fieldKey": "contact.iban",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:05.259Z",
      "standard": false
    },
    {
      "id": "3bHJhiI5BqvSuDGcu4Ss",
      "name": "Total appointments booked for BT_AUG",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_aug",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:11:08.114Z",
      "standard": false
    },
    {
      "id": "66T0ekeZM46tfubTzPPf",
      "name": "What is your current booking system",
      "model": "contact",
      "fieldKey": "contact.what_is_your_current_booking_system",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:39.407Z",
      "standard": false
    },
    {
      "id": "60X4s7LQI8jcdpHCbATf",
      "name": "Gift Card QR Code Url",
      "model": "contact",
      "fieldKey": "contact.gift_card_qr_code_url",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.559Z",
      "standard": false
    },
    {
      "id": "6Btoirf6MJNUO81dB8Ko",
      "name": "Frühere Operationen",
      "model": "contact",
      "fieldKey": "contact.frhere_operationen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:43.336Z",
      "standard": false
    },
    {
      "id": "6VCQZyV4h4CeoHDe21IM",
      "name": "Google Click ID",
      "model": "contact",
      "fieldKey": "contact.google_click_id",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:46:14.747Z",
      "standard": false
    },
    {
      "id": "6e6BRyB6ohZ17UjkI2lF",
      "name": "Augen (z.B Fehlsichtigkeit, Brillenträger)-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.augen_zb_fehlsichtigkeit_brillentrgererkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:12.005Z",
      "standard": false
    },
    {
      "id": "75HqzJZ7BlAws0tF1it8",
      "name": "UTM Campaign",
      "model": "contact",
      "fieldKey": "contact.utm_campaign",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:43:33.072Z",
      "standard": false
    },
    {
      "id": "7CfjqiqdtrwNFFROYs0w",
      "name": "hello 2645 1234",
      "model": "contact",
      "fieldKey": "contact.hello_2645_1234",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:05:36.831Z",
      "standard": false
    },
    {
      "id": "7KiGPXaJnQcg3ANeHo4s",
      "name": "Name des Gynäkologen",
      "model": "contact",
      "fieldKey": "contact.name_des_gynkologen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:46.849Z",
      "standard": false
    },
    {
      "id": "7fL7mamDIv09OTF7WdqA",
      "name": "Gift Card Send Email",
      "model": "contact",
      "fieldKey": "contact.gift_card_send_email",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:06:23.131Z",
      "standard": false
    },
    {
      "id": "7iDZt4deOjZ9cMjo1wjQ",
      "name": "Total appointments booked for BT_LIPO_SIE",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_lipo_sie",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:22:21.112Z",
      "standard": false
    },
    {
      "id": "93LXLrDsT748TkMXID0H",
      "name": "2. Telefonnummer",
      "model": "contact",
      "fieldKey": "contact.2_telefonnummer",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:16:59.342Z",
      "standard": false
    },
    {
      "id": "9JS7A1SgjCTpu3bjf2qH",
      "name": "Folgeskostenversicherung bereits vorhanden?",
      "model": "contact",
      "fieldKey": "contact.folgeskostenversicherung_bereits_vorhanden",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:16:58.577Z",
      "standard": false
    },
    {
      "id": "9UyAfKc6RzMiT1rS1fhO",
      "name": "Mitversichert bei (Name)",
      "model": "contact",
      "fieldKey": "contact.mitversichert_bei_name",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:03.792Z",
      "standard": false
    },
    {
      "id": "9X3YSoPOhBsr1VhA4f5q",
      "name": "Gift Card Quantity",
      "model": "contact",
      "fieldKey": "contact.gift_card_quantity",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:04:48.747Z",
      "standard": false
    },
    {
      "id": "9xKFWHwxncFPuY8j8eOE",
      "name": "What is your budget for implementing new solutions",
      "model": "contact",
      "fieldKey": "contact.what_is_your_budget_for_implementing_new_solutions",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:39.840Z",
      "standard": false
    },
    {
      "id": "AbWLscyAHn7ahJ2JWbjw",
      "name": "Total appointments booked for BT_OSS_ALT",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_oss_alt",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:21:55.315Z",
      "standard": false
    },
    {
      "id": "ANcXbgdt7FNzz8p4yYpg",
      "name": "Gift Card_Message",
      "model": "contact",
      "fieldKey": "contact.gift_card_message",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:48:19.945Z",
      "standard": false
    },
    {
      "id": "BY1i2CXOBLlbieSNGBBR",
      "name": "Autoimmunerkrankungen",
      "model": "contact",
      "fieldKey": "contact.autoimmunerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:11:05.589Z",
      "standard": false
    },
    {
      "id": "Bnetwl001umy06a71biE",
      "name": "Are you prepared to make an investment today to revolutionize your business",
      "model": "contact",
      "fieldKey": "contact.are_you_prepared_to_make_an_investment_today_to_revolutionize_your_business",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:57:16.695Z",
      "standard": false
    },
    {
      "id": "BLgYtiEipe3GsBnz2a0E",
      "name": "What is your website URL",
      "model": "contact",
      "fieldKey": "contact.what_is_your_website_url",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:42.887Z",
      "standard": false
    },
    {
      "id": "CLTrC30AJ7m648nC3g45",
      "name": "Newsletter erwünscht",
      "model": "contact",
      "fieldKey": "contact.newsletter_erwnscht",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:19.262Z",
      "standard": false
    },
    {
      "id": "CUGay3sDOXx4A2Ikcgcc",
      "name": "Gift Card Street Address",
      "model": "contact",
      "fieldKey": "contact.gift_card_street_address",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:23:29.351Z",
      "standard": false
    },
    {
      "id": "D0Um6g0gGZCXdxG1XR5U",
      "name": "Empfohlen von",
      "model": "contact",
      "fieldKey": "contact.empfohlen_von",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:08.234Z",
      "standard": false
    },
    {
      "id": "CYaoFAUUjbIDyQpcdd9Y",
      "name": "Gift Card - Send Email At 5",
      "model": "contact",
      "fieldKey": "contact.gift_card__send_email_at_5",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:01:20.521Z",
      "standard": false
    },
    {
      "id": "DA8T7SN6pVAG4Eq13uK6",
      "name": "UTM Referrer",
      "model": "contact",
      "fieldKey": "contact.utm_referrer",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:44:50.138Z",
      "standard": false
    },
    {
      "id": "ChlbLJmNE21gn7437KGA",
      "name": "Total appointments booked for OP_ITN_IMPL",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_op_itn_impl",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:13.439Z",
      "standard": false
    },
    {
      "id": "DRXiGIASIOm1aDAPnxuz",
      "name": "Total appointments booked for Hautanalyse",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_hautanalyse",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:49:11.207Z",
      "standard": false
    },
    {
      "id": "DcWCpCvrX9cmWRJf0VyQ",
      "name": "Total appointments booked for Blutabnahme",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_blutabnahme",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 8950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T16:34:24.194Z",
      "standard": false
    },
    {
      "id": "DeLvG0X6BfivEz56vSJ5",
      "name": "Gift Card Recipient City",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_city",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.564Z",
      "standard": false
    },
    {
      "id": "DqhvUCKrczMYe46zURmI",
      "name": "Familiärer Brustkrebs-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.familirer_brustkrebserkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:15.589Z",
      "standard": false
    },
    {
      "id": "DtXEFCHVp5DbEirjrWTs",
      "name": "Last appointment location",
      "model": "contact",
      "fieldKey": "contact.last_appointment_location",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 1200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T21:14:25.760Z",
      "standard": false
    },
    {
      "id": "DwR1pf1fYUkv3VxS7G57",
      "name": "Schwangerschaft ausgeschlossen?",
      "model": "contact",
      "fieldKey": "contact.schwangerschaft_ausgeschlossen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:48.421Z",
      "standard": false
    },
    {
      "id": "EovpcfmDHl6puyMSLsDz",
      "name": "Magenballon/-Band-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.magenballonbanderkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:16.753Z",
      "standard": false
    },
    {
      "id": "FWrxfEoJSxcM8KRE1Y0j",
      "name": "Frühere ästhetische Behandlungen?",
      "model": "contact",
      "fieldKey": "contact.frhere_sthetische_behandlungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6700,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:18.780Z",
      "standard": false
    },
    {
      "id": "GOSvLU6nJ7Q8S4srqc6T",
      "name": "CliniCore Profile",
      "model": "contact",
      "fieldKey": "contact.clinicore_profile",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-16T20:57:04.035Z",
      "standard": false
    },
    {
      "id": "GDkt9Brq1Ma8k4SQgnDe",
      "name": "Patient ID",
      "model": "contact",
      "fieldKey": "contact.patient_id",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:46.247Z",
      "standard": false
    },
    {
      "id": "GmvWvAQYtE6Uhq9kaBog",
      "name": "Gift Card Recipient Address To",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_address_to",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.563Z",
      "standard": false
    },
    {
      "id": "GKz244aE1dFwr8uFrrzy",
      "name": "Gift Card Price",
      "model": "contact",
      "fieldKey": "contact.gift_card_price",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:04:09.551Z",
      "standard": false
    },
    {
      "id": "HHznjKMkeOHMeHQCVEPq",
      "name": "Total appointments booked for FU_LU",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_fu_lu",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 8900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T15:45:06.961Z",
      "standard": false
    },
    {
      "id": "HENl6rSBoLx8ck7WcUDo",
      "name": "UTM Source",
      "model": "contact",
      "fieldKey": "contact.utm_source",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:45:59.777Z",
      "standard": false
    },
    {
      "id": "Hzfm7cY8rHukZMWdnJ7P",
      "name": "Bisherige Vollnarkose?",
      "model": "contact",
      "fieldKey": "contact.bisherige_vollnarkose",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:18.313Z",
      "standard": false
    },
    {
      "id": "I2RtCCELLWC1INu5JDpp",
      "name": "Haut-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.hauterkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:12.968Z",
      "standard": false
    },
    {
      "id": "I8yY300p8ksEIVED4E8L",
      "name": "Total appointments booked for BT_OLI_AKB",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_oli_akb",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:29:15.134Z",
      "standard": false
    },
    {
      "id": "JFDPS1JzZnjII47eBe6T",
      "name": "Gift Card Street Address 2",
      "model": "contact",
      "fieldKey": "contact.gift_card_street_address_2",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:24:19.213Z",
      "standard": false
    },
    {
      "id": "JMnTpbq4Rp32Fe0Evrzo",
      "name": "Gift Card Shipping",
      "model": "contact",
      "fieldKey": "contact.gift_card_shipping",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:07:40.127Z",
      "standard": false
    },
    {
      "id": "JaX448pYE8NOwCs8HzEB",
      "name": "Notiz",
      "model": "contact",
      "fieldKey": "contact.notiz",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:40.424Z",
      "standard": false
    },
    {
      "id": "LLC2Rws8g5e43o4F62yA",
      "name": "Gift Card QR Code",
      "model": "contact",
      "fieldKey": "contact.gift_card_qr_code",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12700,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T09:41:10.881Z",
      "standard": false
    },
    {
      "id": "LjXOfdcr6U9wTQnY5C6t",
      "name": "Sonstige Erkrankungen:",
      "model": "contact",
      "fieldKey": "contact.sonstige_erkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:11.160Z",
      "standard": false
    },
    {
      "id": "KzV7jPuYGoujFik9Ipmr",
      "name": "Chronische Erkrankungen",
      "model": "contact",
      "fieldKey": "contact.chronische_erkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:03.059Z",
      "standard": false
    },
    {
      "id": "LuSS5nizYXZucFfP2oO7",
      "name": "Nahrungsergänzungsmittel die blutverdünnend sind",
      "model": "contact",
      "fieldKey": "contact.nahrungsergnzungsmittel_die_blutverdnnend_sind",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:08.975Z",
      "standard": false
    },
    {
      "id": "M80fMquqF4ibD4ebL2ya",
      "name": "Gewicht (kg)",
      "model": "contact",
      "fieldKey": "contact.gewicht_kg",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:42.888Z",
      "standard": false
    },
    {
      "id": "Mitcj2OpuqPjqSBgpHbt",
      "name": "Total appointments booked for BT_BDP",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_bdp",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:11:07.273Z",
      "standard": false
    },
    {
      "id": "Ml95eEnZvcqaBRXfm81S",
      "name": "Message",
      "model": "contact",
      "fieldKey": "contact.message",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:47:15.601Z",
      "standard": false
    },
    {
      "id": "O7sGo4nBiJBWSYBWs1c3",
      "name": "Gift Card State",
      "model": "contact",
      "fieldKey": "contact.gift_card_state",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:25:01.815Z",
      "standard": false
    },
    {
      "id": "P5ICH4hVU39grOhlXPG4",
      "name": "Total appointments booked for BT_Nase_SS",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_nase_ss",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:28:08.456Z",
      "standard": false
    },
    {
      "id": "OSVRcRzHqaa8OQJ9lqoX",
      "name": "Total appointments booked for Bruststraffung",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bruststraffung",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:11:08.855Z",
      "standard": false
    },
    {
      "id": "PALwVZWaQb4ueRFWP9Gl",
      "name": "Versichertenkategorie",
      "model": "contact",
      "fieldKey": "contact.versichertenkategorie",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:11.894Z",
      "standard": false
    },
    {
      "id": "PebtEiKo2e4XxtKM3cm4",
      "name": "BIC",
      "model": "contact",
      "fieldKey": "contact.bic",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:05.982Z",
      "standard": false
    },
    {
      "id": "QBcliVrBWQeq0wmUYimi",
      "name": "Gift Card - Send Email At 4",
      "model": "contact",
      "fieldKey": "contact.gift_card__send_email_at_4",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:01:20.492Z",
      "standard": false
    },
    {
      "id": "PxqyxDBNsN2PvtT2FaL5",
      "name": "Versicherungsnummer",
      "model": "contact",
      "fieldKey": "contact.versicherungsnummer",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:07.480Z",
      "standard": false
    },
    {
      "id": "QM227gqspzYq1laEjR2s",
      "name": "Telefonnummer des Gynäkologen",
      "model": "contact",
      "fieldKey": "contact.telefonnummer_des_gynkologen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:47.359Z",
      "standard": false
    },
    {
      "id": "Q3fpvFt4Kb4piEuKkhLt",
      "name": "Titel",
      "model": "contact",
      "fieldKey": "contact.titel",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:31:30.525Z",
      "standard": false
    },
    {
      "id": "QU6IsySdJYnrKwM5UyAK",
      "name": "Total appointments booked for BT_ENDO_LU",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_endo_lu",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:22:36.067Z",
      "standard": false
    },
    {
      "id": "RTTfgqtWfeAvIAaKpGaM",
      "name": "Blutdruck-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.blutdruckerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:10.384Z",
      "standard": false
    },
    {
      "id": "RWZBMbDAcUTCQ0iyiO5g",
      "name": "Privatversicherung",
      "model": "contact",
      "fieldKey": "contact.privatversicherung",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:16:57.080Z",
      "standard": false
    },
    {
      "id": "T07nqqisEglj15TbbVW9",
      "name": "Total appointments booked for OP-Nachsorge/FU",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_opnachsorgefu",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T16:34:24.698Z",
      "standard": false
    },
    {
      "id": "TEFvm0KqNgdEmrdjGGJt",
      "name": "hello 2645",
      "model": "contact",
      "fieldKey": "contact.hello_2645",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:05:10.926Z",
      "standard": false
    },
    {
      "id": "U0K8Wr9V81tc9oHZivDW",
      "name": "Zusatzversichert",
      "model": "contact",
      "fieldKey": "contact.zusatzversichert",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:16:57.809Z",
      "standard": false
    },
    {
      "id": "TifgRgR97EQ4MtJxJnP1",
      "name": "Gift Card Recipient Name",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_name",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:05:32.449Z",
      "standard": false
    },
    {
      "id": "UYVBX157l0PRuaIv1mmL",
      "name": "UTM MatchType",
      "model": "contact",
      "fieldKey": "contact.utm_matchtype",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:45:31.455Z",
      "standard": false
    },
    {
      "id": "V9FRuSD63NhS4FD0uRq5",
      "name": "Telefon Privat",
      "model": "contact",
      "fieldKey": "contact.telefon_privat",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:44.302Z",
      "standard": false
    },
    {
      "id": "VAJ0Rzxg5y29UXoPpxOA",
      "name": "Komplikationen bei ästhetische Behandlungen?",
      "model": "contact",
      "fieldKey": "contact.komplikationen_bei_sthetische_behandlungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 8750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T14:42:33.813Z",
      "standard": false
    },
    {
      "id": "Uc1Z9Pxmwx520ZT9Q0HR",
      "name": "Gift Card City",
      "model": "contact",
      "fieldKey": "contact.gift_card_city",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:22:37.610Z",
      "standard": false
    },
    {
      "id": "UytKaKnJAGmsAaScnIPo",
      "name": "Message",
      "model": "contact",
      "fieldKey": "contact.message",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:47:13.611Z",
      "standard": false
    },
    {
      "id": "VY2wJLrJxv8gC34H3MOd",
      "name": "Kontakt erlaubt",
      "model": "contact",
      "fieldKey": "contact.kontakt_erlaubt",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:45:41.222Z",
      "standard": false
    },
    {
      "id": "Vz3TDUgHYLg2GUwtfIfQ",
      "name": "hello",
      "model": "contact",
      "fieldKey": "contact.hello",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:03:14.571Z",
      "standard": false
    },
    {
      "id": "WcOgGoGIAPgbN4rdLH7N",
      "name": "How soon are you looking to implement our premium solutions",
      "model": "contact",
      "fieldKey": "contact.how_soon_are_you_looking_to_implement_our_premium_solutions",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:55.260Z",
      "standard": false
    },
    {
      "id": "XOp24UhkOzVJfk0zX4Hq",
      "name": "BMI",
      "model": "contact",
      "fieldKey": "contact.bmi",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:12.663Z",
      "standard": false
    },
    {
      "id": "Yz0LFDMEebJsuoh9CABC",
      "name": "Total appointments booked for BT_OLI_ALT",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_oli_alt",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:29:05.756Z",
      "standard": false
    },
    {
      "id": "YT4JFdcmxDmrdruLUZeK",
      "name": "Allergien",
      "model": "contact",
      "fieldKey": "contact.allergien",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:43.838Z",
      "standard": false
    },
    {
      "id": "aK69D0B96K8RW09JsvGE",
      "name": "Lunge-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.lungeerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:11.389Z",
      "standard": false
    },
    {
      "id": "ZNrWIu7fp5odb0jvFtE8",
      "name": "Gift Card Recipient Email",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_email",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-03T09:15:12.650Z",
      "standard": false
    },
    {
      "id": "abaZAXfaa8ZI5mBQ2cLG",
      "name": "Coupon Code",
      "model": "contact",
      "fieldKey": "contact.coupon_code",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11700,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:28:46.105Z",
      "standard": false
    },
    {
      "id": "b8B7zKc5lCXsGC6tCZ89",
      "name": "Titel (nachgestellt)",
      "model": "contact",
      "fieldKey": "contact.titel_nachgestellt",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:16:56.369Z",
      "standard": false
    },
    {
      "id": "ca9E822VdS5Lkmdr6BMW",
      "name": "hello2",
      "model": "contact",
      "fieldKey": "contact.hello2",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:03:46.055Z",
      "standard": false
    },
    {
      "id": "cwLvqmGNkDtj6GBMW0J6",
      "name": "Größe (cm)",
      "model": "contact",
      "fieldKey": "contact.gre_cm",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:42.408Z",
      "standard": false
    },
    {
      "id": "cKE9hUQghlgWW9725W5n",
      "name": "Gift Card - Send Email At 3",
      "model": "contact",
      "fieldKey": "contact.gift_card__send_email_at_3",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T13:00:17.552Z",
      "standard": false
    },
    {
      "id": "cwjWzbwTW8ZLjPX2BOV3",
      "name": "Neurologische Erkrankungen (z.b Epilepsie)-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.neurologische_erkrankungen_zb_epilepsieerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:13.451Z",
      "standard": false
    },
    {
      "id": "d013XPnADQ6yBPvK9oHb",
      "name": "Gift Card Coupon Code",
      "model": "contact",
      "fieldKey": "contact.gift_card_coupon_code",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T09:43:10.135Z",
      "standard": false
    },
    {
      "id": "d0EN4er55GtQIUvw6UrY",
      "name": "Gift Card Shipping Price",
      "model": "contact",
      "fieldKey": "contact.gift_card_shipping_price",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.503Z",
      "standard": false
    },
    {
      "id": "dPIOQ9AoHVfakj3qHYYw",
      "name": "Tumorerkrankungen/Krebs-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.tumorerkrankungenkrebserkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:14.908Z",
      "standard": false
    },
    {
      "id": "csXgpnDm4IKH6s88cgfr",
      "name": "Regelmäßge Medikation(Selbstangabe)?",
      "model": "contact",
      "fieldKey": "contact.regelmge_medikationselbstangabe",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:11:06.456Z",
      "standard": false
    },
    {
      "id": "ctcJJtBqeMDTJTTj1OUT",
      "name": "Newsletter erwünscht für Rabatt Aktionen ",
      "model": "contact",
      "fieldKey": "contact.newsletter_erwnscht_fr_rabatt_aktionen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10700,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:00.093Z",
      "standard": false
    },
    {
      "id": "du7duGzzQVhtAdfgWwNA",
      "name": "Zustimmung WAHonline",
      "model": "contact",
      "fieldKey": "contact.zustimmung_wahonline",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:06.744Z",
      "standard": false
    },
    {
      "id": "emPUWftRozG1r7rmTLIR",
      "name": "Krankenversicherung",
      "model": "contact",
      "fieldKey": "contact.krankenversicherung",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:43.815Z",
      "standard": false
    },
    {
      "id": "fJbbvR1g0raGlR10ARId",
      "name": "Last appointment treated by",
      "model": "contact",
      "fieldKey": "contact.last_appointment_treated_by",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 1150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T21:14:25.222Z",
      "standard": false
    },
    {
      "id": "fCp0yIRxrgpnpJKvFucL",
      "name": "Dauerdiagnosen",
      "model": "contact",
      "fieldKey": "contact.dauerdiagnosen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:02.302Z",
      "standard": false
    },
    {
      "id": "euJkgpd4kfQDGm4wZG1L",
      "name": "Gift Card Send Method",
      "model": "contact",
      "fieldKey": "contact.gift_card_send_method",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:06:59.701Z",
      "standard": false
    },
    {
      "id": "exPXF0a59BQCU2zUcsTW",
      "name": "Interessiert an",
      "model": "contact",
      "fieldKey": "contact.interessiert_an",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5150,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:10:16.127Z",
      "standard": false
    },
    {
      "id": "fUWd5NjtcjzfYnFxyQfT",
      "name": "Last appointment resource",
      "model": "contact",
      "fieldKey": "contact.last_appointment_resource",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 1250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T21:14:26.227Z",
      "standard": false
    },
    {
      "id": "f3Anver7y1guHN4SkUMg",
      "name": "Niere-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.niereerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:10.849Z",
      "standard": false
    },
    {
      "id": "grDq4w7XoXAPeJHZkxr1",
      "name": "Beruf",
      "model": "contact",
      "fieldKey": "contact.beruf",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:07.936Z",
      "standard": false
    },
    {
      "id": "gZJZGvLCPl3wPRA23mtB",
      "name": "Gift Card Recipient State",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_state",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.556Z",
      "standard": false
    },
    {
      "id": "gpqjidIYCHhzTRIFW5g9",
      "name": "Total appointments booked for BT_LIPO_ALT",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_lipo_alt",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 8000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:55:26.671Z",
      "standard": false
    },
    {
      "id": "h970WGVE8JGSdBrQbRTG",
      "name": "Rheuma/Arthritis-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.rheumaarthritiserkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:13.944Z",
      "standard": false
    },
    {
      "id": "ibrOFjejwV7XH265XY7F",
      "name": "Infektionskrankheiten?",
      "model": "contact",
      "fieldKey": "contact.infektionskrankheiten",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:08.924Z",
      "standard": false
    },
    {
      "id": "hNyrnTdCF4YP5Qbi88AR",
      "name": "What is your business type",
      "model": "contact",
      "fieldKey": "contact.what_is_your_business_type",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:57:01.003Z",
      "standard": false
    },
    {
      "id": "igTJzZrNMSDhxl01VMlr",
      "name": "CC Patient ID",
      "model": "contact",
      "fieldKey": "contact.cc_patient_id",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 4400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-12T18:23:53.353Z",
      "standard": false
    },
    {
      "id": "jCMrFuAa4upHylNB4VEr",
      "name": "AFFILIATE ID",
      "model": "contact",
      "fieldKey": "contact.affiliate_id",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:49:09.281Z",
      "standard": false
    },
    {
      "id": "jnWEpKKcMjjAy7CYC43t",
      "name": "Gift Card Address To",
      "model": "contact",
      "fieldKey": "contact.gift_card_address_to",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T08:21:57.417Z",
      "standard": false
    },
    {
      "id": "joLDPmLuTxcXVGAVTG1S",
      "name": "Gynäkologen?",
      "model": "contact",
      "fieldKey": "contact.gynkologen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:46.364Z",
      "standard": false
    },
    {
      "id": "jhSjLXiYRGoyYwA5pYxy",
      "name": "Hausarzt",
      "model": "contact",
      "fieldKey": "contact.hausarzt",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:41.382Z",
      "standard": false
    },
    {
      "id": "jvH88iR15Yf65C0thwvh",
      "name": "Komplikationen bei früheren Operationen?",
      "model": "contact",
      "fieldKey": "contact.komplikationen_bei_frheren_operationen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:48.895Z",
      "standard": false
    },
    {
      "id": "kpVmpsDELP6Y2Ux0DKrk",
      "name": "Psyche (z.B Depressionen)-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.psyche_zb_depressionenerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:16.086Z",
      "standard": false
    },
    {
      "id": "kjzsnGXR2dPpk6k7RfnI",
      "name": "Stoffwechsel (Schilddrüse/Diabetes/Gicht)-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.stoffwechsel_schilddrsediabetesgichterkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:14.451Z",
      "standard": false
    },
    {
      "id": "kuxxLL7XJKOdIK9LK5et",
      "name": "Herz-Kreislauf-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.herzkreislauferkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:09.437Z",
      "standard": false
    },
    {
      "id": "kR9Eg3jYR7IY2szuKJq4",
      "name": "Adresse des Hausarztes",
      "model": "contact",
      "fieldKey": "contact.adresse_des_hausarztes",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:45.872Z",
      "standard": false
    },
    {
      "id": "l2BKFwQesrUyZfjrAqf5",
      "name": "Knochen-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.knochenerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:12.500Z",
      "standard": false
    },
    {
      "id": "lQB2iHrxv5wSI43gLOpt",
      "name": "Gift Card - Send Email At",
      "model": "contact",
      "fieldKey": "contact.gift_card__send_email_at",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 12850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-03T09:57:12.946Z",
      "standard": false
    },
    {
      "id": "lbGYt4flcdsHMEL90WDo",
      "name": "Blutgerinnung-Erkrankungen?",
      "model": "contact",
      "fieldKey": "contact.blutgerinnungerkrankungen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:09.911Z",
      "standard": false
    },
    {
      "id": "lifoTIAOP8lRXNLZM5Uq",
      "name": "Gift Card Recipient Street Address",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_street_address",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.586Z",
      "standard": false
    },
    {
      "id": "lY7dAuhpDZBLkckqKvsg",
      "name": "Gift Card Email scheduled date",
      "model": "contact",
      "fieldKey": "contact.gift_card_email_scheduled_date",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.564Z",
      "standard": false
    },
    {
      "id": "lwZq5vInHwW0vg1OlEux",
      "name": "Gift Card Schedule Time",
      "model": "contact",
      "fieldKey": "contact.gift_card_schedule_time",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.548Z",
      "standard": false
    },
    {
      "id": "n4quQR46ydFYs4tfYK3Z",
      "name": "Total appointments booked for BT_AUG_SIE",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_aug_sie",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:29:37.592Z",
      "standard": false
    },
    {
      "id": "m6CGMuzcWe13soIMdKYm",
      "name": "Mitversichert bei (Nummer)",
      "model": "contact",
      "fieldKey": "contact.mitversichert_bei_nummer",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:04.523Z",
      "standard": false
    },
    {
      "id": "nalshtF0JMJeGB1YdMbX",
      "name": "Infektionserkrankungen (Hepatitis, HIV etc.)",
      "model": "contact",
      "fieldKey": "contact.infektionserkrankungen_hepatitis_hiv_etc",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:09.739Z",
      "standard": false
    },
    {
      "id": "o6Yv79A7cZ4N5txpaFMR",
      "name": "Total appointments booked for BT_BSTR_AKB",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_bstr_akb",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:29:25.297Z",
      "standard": false
    },
    {
      "id": "o8cpudnBvJx6d9CnUOq0",
      "name": "UTM Content",
      "model": "contact",
      "fieldKey": "contact.utm_content",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:44:05.518Z",
      "standard": false
    },
    {
      "id": "p5yCu2mAYAfjLmrcOE7d",
      "name": "Telefon Geschäftlich",
      "model": "contact",
      "fieldKey": "contact.telefon_geschftlich",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:55:25.744Z",
      "standard": false
    },
    {
      "id": "pueAVrH8w39gwaERVltt",
      "name": "Familienstand",
      "model": "contact",
      "fieldKey": "contact.familienstand",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:00.827Z",
      "standard": false
    },
    {
      "id": "pVTEMSUTicIRuICnuLsa",
      "name": "Gerinnungsstörungen mit Thromboseneigung oder Blutungsneigung",
      "model": "contact",
      "fieldKey": "contact.gerinnungsstrungen_mit_thromboseneigung_oder_blutungsneigung",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:10.444Z",
      "standard": false
    },
    {
      "id": "qdV5pmNqtsnFO9bTOtsF",
      "name": "Telefonnummer des Hausarztes",
      "model": "contact",
      "fieldKey": "contact.telefonnummer_des_hausarztes",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:45.378Z",
      "standard": false
    },
    {
      "id": "r6r2HcydJizznQM2FfMZ",
      "name": "Total appointments booked for BT_Nase_AKB",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_nase_akb",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 5400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:28:20.529Z",
      "standard": false
    },
    {
      "id": "rAuLY636q0gRdzIi3ZfT",
      "name": "FB Click ID",
      "model": "contact",
      "fieldKey": "contact.fb_click_id",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:46:32.805Z",
      "standard": false
    },
    {
      "id": "s3JZ6hKoWnRhtEGxlk7U",
      "name": "Fift For",
      "model": "contact",
      "fieldKey": "contact.fift_for",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:42:49.849Z",
      "standard": false
    },
    {
      "id": "tYPW6az9GIiTSbdsI3yq",
      "name": "Name des Hausarztes",
      "model": "contact",
      "fieldKey": "contact.name_des_hausarztes",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:44.854Z",
      "standard": false
    },
    {
      "id": "tuFfHRheksH55iDRw0Gx",
      "name": "Wie sind Sie auf uns aufmerksam geworden?",
      "model": "contact",
      "fieldKey": "contact.wie_sind_sie_auf_uns_aufmerksam_geworden",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:49.846Z",
      "standard": false
    },
    {
      "id": "tgu0C9tdDbQCVsfeHSSu",
      "name": "Total appointments booked for BT_BDP_AKB",
      "model": "contact",
      "fieldKey": "contact.total_appointments_booked_for_bt_bdp_akb",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:50.807Z",
      "standard": false
    },
    {
      "id": "u12JflThlJJbfIXfZk8m",
      "name": "Corona Infektion?",
      "model": "contact",
      "fieldKey": "contact.corona_infektion",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:17.717Z",
      "standard": false
    },
    {
      "id": "v37o5PKlT03jUcTXrTtg",
      "name": "Life Time Value",
      "model": "contact",
      "fieldKey": "contact.life_time_value",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T12:08:53.455Z",
      "standard": false
    },
    {
      "id": "vQXVoDsNLA4NekfVclIh",
      "name": "Gift Card Discount Code",
      "model": "contact",
      "fieldKey": "contact.gift_card_discount_code",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-24T14:42:35.307Z",
      "standard": false
    },
    {
      "id": "w85lihoUzLPgoDlxHuv7",
      "name": "On a scale of 1-10, how ready are you to transform your business with our solutions?",
      "model": "contact",
      "fieldKey": "contact.on_a_scale_of_110_how_ready_are_you_to_transform_your_business_with_our_solutions",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-05T13:56:48.296Z",
      "standard": false
    },
    {
      "id": "wSEtyigxh3ZJ6vwF8VYe",
      "name": "What premium features are you most interested in",
      "model": "contact",
      "fieldKey": "contact.what_premium_features_are_you_most_interested_in",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9250,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:41.248Z",
      "standard": false
    },
    {
      "id": "wCG0i8l4PlRFA86TaCjO",
      "name": "Message",
      "model": "contact",
      "fieldKey": "contact.message",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:46:42.418Z",
      "standard": false
    },
    {
      "id": "x6tMwKTXsHPeZzJLUlu2",
      "name": "UTM Medium",
      "model": "contact",
      "fieldKey": "contact.utm_medium",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9700,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-15T04:44:26.792Z",
      "standard": false
    },
    {
      "id": "xNDpLB78dNKOwMqGRthW",
      "name": "Medikation",
      "model": "contact",
      "fieldKey": "contact.medikation",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 10800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-22T13:17:01.559Z",
      "standard": false
    },
    {
      "id": "yLHSf5M9N7aoYnu5e1qs",
      "name": "Adresse des Gynäkologen",
      "model": "contact",
      "fieldKey": "contact.adresse_des_gynkologen",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 7200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:47.923Z",
      "standard": false
    },
    {
      "id": "yLFCWCk6kHtHu5neFvdX",
      "name": "Total Appointments",
      "model": "contact",
      "fieldKey": "contact.total_appointments",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2023-10-27T20:50:45.792Z",
      "standard": false
    },
    {
      "id": "xwbV5fxn7Pefdzz17jLq",
      "name": "Message",
      "model": "contact",
      "fieldKey": "contact.message",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 11900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-10-31T07:47:07.896Z",
      "standard": false
    },
    {
      "id": "yF2ar4rpw4z4GU7udIYf",
      "name": "Corona Impfung?",
      "model": "contact",
      "fieldKey": "contact.corona_impfung",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 6550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-03-14T13:30:17.223Z",
      "standard": false
    },
    {
      "id": "zf3tw5pMCNSaKZGUp7xE",
      "name": "Gift Card Recipient Street Address 2",
      "model": "contact",
      "fieldKey": "contact.gift_card_recipient_street_address_2",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-11-25T04:39:40.561Z",
      "standard": false
    },
    {
      "id": "zTrcfJnHWiQz0rjLkZ4a",
      "name": "How did you hear about us",
      "model": "contact",
      "fieldKey": "contact.how_did_you_hear_about_us",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 9200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2024-08-04T13:56:40.463Z",
      "standard": false
    },
    {
      "id": "2BDuciugmDs5yS14iZnu",
      "name": "Custom Field",
      "model": "contact",
      "fieldKey": "contact.custom_field",
      "placeholder": "Placeholder Text",
      "dataType": "TEXT",
      "position": 13400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:11:58.432Z",
      "standard": false
    },
    {
      "id": "FmjHuD5GqqiNiyS7uYsZ",
      "name": "Total Invoices",
      "model": "contact",
      "fieldKey": "contact.total_invoices",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13450,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:14.145Z",
      "standard": false
    },
    {
      "id": "qEkIINYxIc9Ri1MTQG2s",
      "name": "Total Payments",
      "model": "contact",
      "fieldKey": "contact.total_payments",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13500,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:15.255Z",
      "standard": false
    },
    {
      "id": "wkjDRpALH5P9tmvN1ja7",
      "name": "Due amount",
      "model": "contact",
      "fieldKey": "contact.due_amount",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13550,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:16.350Z",
      "standard": false
    },
    {
      "id": "v5rtT779WotVqrsoQeEL",
      "name": "Credit amount",
      "model": "contact",
      "fieldKey": "contact.credit_amount",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13600,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:17.304Z",
      "standard": false
    },
    {
      "id": "HuMO48hgBsmNhd1Xe84h",
      "name": "Total Invoiced Amount",
      "model": "contact",
      "fieldKey": "contact.total_invoiced_amount",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13650,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:18.692Z",
      "standard": false
    },
    {
      "id": "iJfR8pBHJt0OvNo3tYZh",
      "name": "Total Spent",
      "model": "contact",
      "fieldKey": "contact.total_spent",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13700,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:19.650Z",
      "standard": false
    },
    {
      "id": "CmOq6wXCFxrHoBnw1skx",
      "name": "Average Invoice Amount",
      "model": "contact",
      "fieldKey": "contact.average_invoice_amount",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13750,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:20.593Z",
      "standard": false
    },
    {
      "id": "jtcvCCkSO6sP3AXzDf9O",
      "name": "Average Payment Amount",
      "model": "contact",
      "fieldKey": "contact.average_payment_amount",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13800,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:21.696Z",
      "standard": false
    },
    {
      "id": "JZvzWePlliLRU3mALnSu",
      "name": "Lifetime Value",
      "model": "contact",
      "fieldKey": "contact.lifetime_value",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13850,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:22.650Z",
      "standard": false
    },
    {
      "id": "tfn70dM4Pg994XCXEFpl",
      "name": "Last Invoice Date",
      "model": "contact",
      "fieldKey": "contact.last_invoice_date",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13900,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-24T22:29:24.047Z",
      "standard": false
    },
    {
      "id": "UWkryfEMwQH5IQVVYjC3",
      "name": "Zu uns gekommen durch",
      "model": "contact",
      "fieldKey": "contact.zu_uns_gekommen_durch",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 13950,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-25T23:04:59.092Z",
      "standard": false
    },
    {
      "id": "qeH4ucAxG4kzFWq8FB0t",
      "name": "Telefon Mobil",
      "model": "contact",
      "fieldKey": "contact.telefon_mobil",
      "placeholder": "",
      "dataType": "PHONE",
      "position": 14000,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-25T23:52:45.985Z",
      "standard": false
    },
    {
      "id": "HTxSJfSupWhHJyymfRDQ",
      "name": "Terminerinnerungen",
      "model": "contact",
      "fieldKey": "contact.contactreminderdeliverymethods",
      "placeholder": "",
      "dataType": "SINGLE_OPTIONS",
      "position": 14050,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-26T01:25:38.964Z",
      "standard": false,
      "picklistOptions": [
        "SMS",
        "E-Mail"
      ]
    },
    {
      "id": "4gusDW85K5y8CxILJSUi",
      "name": "Terminerinnerungen",
      "model": "contact",
      "fieldKey": "contact.reminderdeliverymethods",
      "placeholder": "",
      "dataType": "SINGLE_OPTIONS",
      "position": 14100,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-26T02:30:17.152Z",
      "standard": false,
      "picklistOptions": [
        "SMS",
        "E-Mail"
      ]
    },
    {
      "id": "KyzIPkMZ9zbmKo3KbCvz",
      "name": "Allergie",
      "model": "contact",
      "fieldKey": "contact.allergie",
      "placeholder": "",
      "dataType": "TEXT",
      "position": 14200,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-26T02:30:20.431Z",
      "standard": false
    },
    {
      "id": "J2HlC6KaTLOLZwyuOu3J",
      "name": "TEST 2 - multiple selection",
      "model": "contact",
      "fieldKey": "contact.test2multipleselection",
      "placeholder": "",
      "dataType": "MULTIPLE_OPTIONS",
      "position": 14300,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-26T15:53:05.181Z",
      "standard": false,
      "picklistOptions": [
        "1",
        "2",
        "3",
        "4",
        "3, 4"
      ]
    },
    {
      "id": "HsgrLxJ1naSZzXeUmG4O",
      "name": "Test3 - Dropdown Multiple",
      "model": "contact",
      "fieldKey": "contact.test3dropdownmultiple",
      "placeholder": "",
      "dataType": "MULTIPLE_OPTIONS",
      "position": 14350,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-26T15:53:08.144Z",
      "standard": false,
      "picklistOptions": [
        "1 - One",
        "2 - Two",
        "3 - Three",
        "4 - Four"
      ]
    },
    {
      "id": "EwFnyvpa16PQvmtb3X4b",
      "name": "Befunde",
      "model": "contact",
      "fieldKey": "contact.befunde",
      "placeholder": "",
      "dataType": "RADIO",
      "position": 14400,
      "documentType": "field",
      "parentId": "RgQQpmZ5XV9dDm4hUsEX",
      "locationId": "CIY0QcIvP7m9TxVWlvy3",
      "dateAdded": "2025-07-26T16:05:08.971Z",
      "standard": false,
      "picklistOptions": [
        "Yes",
        "No"
      ],
      "isAllowedCustomOption": false
    }
  ]