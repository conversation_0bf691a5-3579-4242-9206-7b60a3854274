/**
 * Comprehensive Application Configuration Interface for DermaCare Sync Service
 *
 * This interface defines all configuration values used throughout the application,
 * providing a centralized location for managing settings, timeouts, thresholds,
 * and other configurable parameters.
 *
 * **Configuration Categories:**
 * - Database connection settings
 * - API endpoints and authentication
 * - Cache and performance settings
 * - Buffer and retry configurations
 * - Performance thresholds and limits
 * - Error logging settings
 * - Webhook processing timeouts
 * - Logging configuration and levels
 *
 * @since 1.0.0
 * @version 1.0.0
 */

/**
 * Log levels for filtering log output
 */
export type LogLevel = "DEBUG" | "INFO" | "WARN" | "ERROR";

/**
 * Logging configuration interface
 */
export interface LoggingConfig {
	/** Current log level - messages below this level will be filtered out */
	level: LogLevel;
	/** Whether the application is running in production mode */
	isProduction: boolean;
	/** Whether to include debug information in logs */
	includeDebugInfo: boolean;
}

interface AppConfigs {
	// Database Configuration
	databaseUrl: string;
	// API Configuration
	ccApiDomain: string;
	ccApiKey: string;
	apApiKey: string;
	apApiDomain: string;
	locationID: string;
	apCalendarId: string;
	syncBufferTimeSec: number; // Buffer time in seconds to prevent unnecessary syncs

	// Custom Field Synchronization Configuration
	/** Controls whether missing custom fields should be automatically created in CliniCore when they exist in AutoPatient but not in CC during AP→CC synchronization */
	createMissingFieldsInCC: boolean;
	/** Controls whether missing custom fields should be automatically created in AutoPatient when they exist in CliniCore but not in AP during CC→AP synchronization */
	createMissingCustomFieldsInAP: boolean;

	apCustomFieldsParents: string[];

	// Logging Configuration
	logging: LoggingConfig;
}

/**
 * Centralized configuration object containing all application settings
 *
 * **Configuration Management:**
 * All configuration values are centralized in this file. To modify settings,
 * update the values directly in this configuration object.
 *
 * **Performance Optimization:**
 * All timeout and threshold values are optimized for the 25-second webhook
 * completion requirement while maintaining reliability and user experience.
 */
/**
 * Get environment variable with fallback
 */
const getEnvVar = (_key: string, fallback: string): string => {
	return fallback;
};

/**
 * Parse log level from environment variable
 */
const parseLogLevel = (level: string): LogLevel => {
	const upperLevel = level.toUpperCase() as LogLevel;
	if (["DEBUG", "INFO", "WARN", "ERROR"].includes(upperLevel)) {
		return upperLevel;
	}
	return "INFO"; // Default fallback
};

const configs: AppConfigs = {
	// Database Configuration
	databaseUrl:
		"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",

	// API Configuration
	ccApiDomain: "https://ccdemo.clinicore.eu/api/v1",
	ccApiKey:
		"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
	apApiDomain: "https://services.leadconnectorhq.com",
	apApiKey: "pit-cc1e91e1-bc76-4438-a39e-9a63dec45c9f",
	locationID: "CIY0QcIvP7m9TxVWlvy3",
	apCalendarId: "ZsyisrZNAGBZsLAE60zj",

	// Basic Configuration (optimized for performance)
	syncBufferTimeSec: 60, // 1 minute buffer time to prevent unnecessary syncs

	// Custom Field Synchronization Configuration (defaults to true for backward compatibility)
	createMissingFieldsInCC: true, // Allow AP fields to be created in CC by default
	createMissingCustomFieldsInAP: true, // Allow CC fields to be created in AP by default

	apCustomFieldsParents: [],

	// Logging Configuration
	logging: {
		level: parseLogLevel(getEnvVar("LOG_LEVEL", "INFO")),
		isProduction:
			getEnvVar("NODE_ENV", "development") === "production" ||
			getEnvVar("IS_PRODUCTION", "false") === "true",
		includeDebugInfo: getEnvVar("LOG_DEBUG_INFO", "true") === "true",
	},
};

/**
 * Get a specific top-level configuration value
 * @param key - Configuration key to retrieve
 * @returns The configuration value
 */
export const getConfig = (key: keyof AppConfigs): AppConfigs[typeof key] => {
	const v = configs[key] as AppConfigs[typeof key];
	if (v === undefined || v === null) {
		console.error(`Config ${key} is not defined.`);
		throw new Error(`Config ${key} is not defined.`);
	}
	return v;
};

/**
 * Get all configuration values
 * @returns Complete configuration object
 */
const getConfigs = (): AppConfigs => {
	return configs;
};

/**
 * Validate configuration on startup
 * @throws Error if any required configuration is missing or invalid
 */
export const validateConfig = (): void => {
	const requiredKeys: (keyof AppConfigs)[] = [
		"databaseUrl",
		"ccApiDomain",
		"ccApiKey",
		"apApiDomain",
		"apApiKey",
		"locationID",
		"apCalendarId",
	];

	for (const key of requiredKeys) {
		const value = configs[key];
		if (!value || (typeof value === "string" && value.trim() === "")) {
			throw new Error(`Required configuration ${key} is missing or empty`);
		}
	}

	// Validate logging configuration
	const validLogLevels: LogLevel[] = ["DEBUG", "INFO", "WARN", "ERROR"];
	if (!validLogLevels.includes(configs.logging.level)) {
		throw new Error(
			`Invalid log level: ${configs.logging.level}. Must be one of: ${validLogLevels.join(", ")}`,
		);
	}

	console.log("✅ Configuration validation passed");
	console.log(
		`📊 Log Level: ${configs.logging.level}, Production: ${configs.logging.isProduction}`,
	);
};

export default getConfig;
export { getConfigs };
export type { AppConfigs };
