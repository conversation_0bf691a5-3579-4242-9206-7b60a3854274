/**
 * Centralized Logging Utility for DermaCare DataSync
 *
 * Provides standardized logging functions with consistent formatting,
 * request ID tracking, ISO timestamps, and log level filtering.
 *
 * Features:
 * - Consistent log format: [requestId] [timestamp] message
 * - Log level filtering (DEBUG, INFO, WARN, ERROR)
 * - Production mode support with reduced verbosity
 * - Request context propagation
 * - Performance-optimized for production environments
 */

import { getConfig, type LoggingConfig, type LogLevel } from "@config";
import { getRequestId } from "@/utils/getRequestId";

/**
 * Log level hierarchy for filtering
 */
const LOG_LEVELS: Record<LogLevel, number> = {
	DEBUG: 0,
	INFO: 1,
	WARN: 2,
	ERROR: 3,
};

/**
 * Check if a log level should be output based on current configuration
 */
const shouldLog = (level: LogLevel): boolean => {
	const config = getConfig("logging") as LoggingConfig;
	const currentLevelValue = LOG_LEVELS[config.level];
	const messageLevelValue = LOG_LEVELS[level];
	return messageLevelValue >= currentLevelValue;
};

/**
 * Format log message with consistent structure
 */
const formatLogMessage = (
	requestId: string,
	level: LogLevel,
	message: string,
	data?: unknown,
): string => {
	const timestamp = new Date().toISOString();
	const baseMessage = `[${requestId}] [${timestamp}] [${level}] ${message}`;

	if (data !== undefined) {
		const config = getConfig("logging") as LoggingConfig;
		if (config.includeDebugInfo) {
			return `${baseMessage}\nData: ${JSON.stringify(data, null, 2)}`;
		} else {
			// In production, limit data output to prevent log spam
			const dataStr =
				typeof data === "object" && data !== null
					? `${JSON.stringify(data).substring(0, 200)}...`
					: String(data).substring(0, 200);
			return `${baseMessage} | Data: ${dataStr}`;
		}
	}

	return baseMessage;
};

/**
 * Log debug messages (lowest priority)
 * Only shown when LOG_LEVEL is DEBUG
 * Automatically retrieves request ID from context
 */
export const logDebug = (message: string, data?: unknown): void => {
	if (shouldLog("DEBUG")) {
		const requestId = getRequestId();
		console.log(formatLogMessage(requestId, "DEBUG", message, data));
	}
};

/**
 * Log informational messages
 * Shown when LOG_LEVEL is DEBUG or INFO
 * Automatically retrieves request ID from context
 */
export const logInfo = (message: string, data?: unknown): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		console.log(formatLogMessage(requestId, "INFO", message, data));
	}
};

/**
 * Log warning messages
 * Shown when LOG_LEVEL is DEBUG, INFO, or WARN
 * Automatically retrieves request ID from context
 */
export const logWarn = (message: string, data?: unknown): void => {
	if (shouldLog("WARN")) {
		const requestId = getRequestId();
		console.warn(formatLogMessage(requestId, "WARN", message, data));
	}
};

/**
 * Log error messages (highest priority)
 * Always shown regardless of log level
 * Automatically retrieves request ID from context
 */
export const logError = (message: string, error?: unknown): void => {
	if (shouldLog("ERROR")) {
		const requestId = getRequestId();
		const errorData =
			error instanceof Error
				? { message: error.message, stack: error.stack }
				: error;
		console.error(formatLogMessage(requestId, "ERROR", message, errorData));
	}
};

/**
 * Log API performance metrics
 * Special formatting for API call performance tracking
 * Automatically retrieves request ID from context
 */
export const logApiPerformance = (
	method: string,
	status: number,
	duration: number,
	url: string,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const message = `API Call: [${method}] [${status}] -> ${duration.toFixed(2)}s -> ${url}`;
		console.log(formatLogMessage(requestId, "INFO", message));
	}
};

/**
 * Log database operation performance
 * Special formatting for database operation tracking
 * Automatically retrieves request ID from context
 */
export const logDbPerformance = (
	operation: string,
	duration: number,
	details?: string,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const message = `DB Operation: ${operation} -> ${duration.toFixed(2)}ms${details ? ` -> ${details}` : ""}`;
		console.log(formatLogMessage(requestId, "INFO", message));
	}
};

/**
 * Log processing step completion
 * Used for tracking major processing milestones
 * Automatically retrieves request ID from context
 */
export const logProcessingStep = (
	step: string,
	duration?: number,
	details?: unknown,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const durationStr = duration ? ` (${duration}ms)` : "";
		const message = `Processing: ${step}${durationStr}`;
		console.log(formatLogMessage(requestId, "INFO", message, details));
	}
};

/**
 * Log custom field operations
 * Specialized logging for custom field processing
 * Automatically retrieves request ID from context
 */
export const logCustomField = (
	operation: string,
	fieldName: string,
	details?: unknown,
): void => {
	if (shouldLog("DEBUG")) {
		const requestId = getRequestId();
		const message = `Custom Field ${operation}: ${fieldName}`;
		console.log(formatLogMessage(requestId, "DEBUG", message, details));
	}
};

/**
 * Log webhook events
 * Specialized logging for webhook processing
 * Automatically retrieves request ID from context
 */
export const logWebhook = (
	event: string,
	model: string,
	entityId?: string | number,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const message = `Webhook: ${event} -> ${model}${entityId ? ` -> ID: ${entityId}` : ""}`;
		console.log(formatLogMessage(requestId, "INFO", message));
	}
};

/**
 * Legacy console.log replacement
 * Provides backward compatibility while encouraging migration to structured logging
 * @deprecated Use specific log functions (logInfo, logError, etc.) instead
 */
export const log = (message: string, data?: unknown): void => {
	logInfo(message, data);
};

/**
 * Get current logging configuration
 * Useful for debugging logging issues
 */
export const getLoggingConfig = (): LoggingConfig => {
	return getConfig("logging") as LoggingConfig;
};

/**
 * Check if debug logging is enabled
 * Useful for conditional expensive debug operations
 */
export const isDebugEnabled = (): boolean => {
	return shouldLog("DEBUG");
};

/**
 * Check if production mode is enabled
 * Useful for conditional production optimizations
 */
export const isProductionMode = (): boolean => {
	return (getConfig("logging") as LoggingConfig).isProduction;
};
