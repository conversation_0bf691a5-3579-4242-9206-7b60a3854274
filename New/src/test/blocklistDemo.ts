/**
 * Field Creation Blocklist Demonstration
 *
 * This script demonstrates the new field creation blocklist system that prevents
 * specific AutoPatient custom fields from being created in CliniCore during
 * the custom field synchronization process.
 *
 * Features demonstrated:
 * 1. Exact field name matching
 * 2. Regex pattern matching
 * 3. Case-insensitive matching
 * 4. Blocklist reason tracking
 */

import {
	checkApToCcCreationBlocklist,
	getBlockedApFields,
} from "@/config/standardFieldMappings";

/**
 * Test field names to demonstrate blocklist functionality
 */
const testFields = [
	// Should be blocked - exact matches
	{ name: "pipeline" },
	{ name: "funnel" },
	{ name: "lead_score" },
	{ name: "conversion_tracking" },

	// Should be blocked - regex patterns
	{ name: "internal_id" },
	{ name: "created_at" },
	{ name: "location_id" },
	{ name: "dnd" },
	{ name: "legacy_field" },
	{ name: "test_field" },
	{ name: "zapier_webhook" },

	// Should NOT be blocked
	{ name: "patient_notes" },
	{ name: "allergies" },
	{ name: "medication_list" },
	{ name: "appointment_preference" },
	{ name: "insurance_provider" },
];

/**
 * Demonstrate blocklist functionality
 */
export function demonstrateBlocklist(): void {
	console.log("🚫 Field Creation Blocklist Demonstration");
	console.log("=".repeat(60));

	console.log("\n1. Individual Field Checks:");
	console.log("-".repeat(40));

	for (const field of testFields) {
		const blocklistEntry = checkApToCcCreationBlocklist(field.name);

		if (blocklistEntry) {
			console.log(`❌ BLOCKED: "${field.name}"`);
			console.log(`   Pattern: ${blocklistEntry.pattern}`);
			console.log(`   Reason: ${blocklistEntry.reason}`);
			console.log(`   Type: ${blocklistEntry.isRegex ? "Regex" : "Exact"}`);
		} else {
			console.log(`✅ ALLOWED: "${field.name}"`);
		}
		console.log("");
	}

	console.log("\n2. Batch Processing:");
	console.log("-".repeat(40));

	const blockedFields = getBlockedApFields(testFields);

	console.log(`Total fields tested: ${testFields.length}`);
	console.log(`Blocked fields: ${blockedFields.length}`);
	console.log(`Allowed fields: ${testFields.length - blockedFields.length}`);

	if (blockedFields.length > 0) {
		console.log("\nBlocked field summary:");
		for (const { field, blocklistEntry } of blockedFields) {
			console.log(`  • ${field.name} - ${blocklistEntry.reason}`);
		}
	}

	console.log("\n3. Case Sensitivity Test:");
	console.log("-".repeat(40));

	const caseSensitivityTests = [
		"PIPELINE",
		"Pipeline",
		"pipeline",
		"INTERNAL_ID",
		"Internal_Id",
		"internal_id",
	];

	for (const testName of caseSensitivityTests) {
		const blocklistEntry = checkApToCcCreationBlocklist(testName);
		const status = blocklistEntry ? "BLOCKED" : "ALLOWED";
		console.log(`${status}: "${testName}"`);
	}

	console.log("\n✨ Blocklist demonstration completed!");
}

/**
 * Simulate the integration with custom field synchronization
 */
export function simulateBlocklistIntegration(): void {
	console.log("\n🔄 Simulating Blocklist Integration");
	console.log("=".repeat(60));

	// Simulate AP fields that would be processed during synchronization
	const mockApFields = [
		{ id: "1", name: "patient_allergies", dataType: "TEXT" },
		{ id: "2", name: "pipeline", dataType: "RADIO" },
		{ id: "3", name: "insurance_info", dataType: "TEXT" },
		{ id: "4", name: "internal_id", dataType: "TEXT" },
		{ id: "5", name: "appointment_notes", dataType: "TEXTAREA" },
		{ id: "6", name: "test_field", dataType: "TEXT" },
	];

	console.log("Processing AP fields for CC creation...\n");

	let createdCount = 0;
	let blockedCount = 0;

	for (const apField of mockApFields) {
		const blocklistEntry = checkApToCcCreationBlocklist(apField.name);

		if (blocklistEntry) {
			blockedCount++;
			console.log(`🚫 BLOCKED: "${apField.name}" (ID: ${apField.id})`);
			console.log(`   Reason: ${blocklistEntry.reason}`);
			console.log(`   Would increment creationBlockedCount statistic`);
		} else {
			createdCount++;
			console.log(`✅ CREATING: "${apField.name}" (ID: ${apField.id})`);
			console.log(`   Type: ${apField.dataType}`);
			console.log(`   Would create CC custom field and store mapping`);
		}
		console.log("");
	}

	console.log("Synchronization Summary:");
	console.log(`  Fields processed: ${mockApFields.length}`);
	console.log(`  Fields created: ${createdCount}`);
	console.log(`  Fields blocked: ${blockedCount}`);
	console.log(
		`  Block rate: ${((blockedCount / mockApFields.length) * 100).toFixed(1)}%`,
	);
}

// Run demonstrations if this file is executed directly
// Note: import.meta.main is Deno-specific, commented out for Node.js compatibility
// if (import.meta.main) {
// 	demonstrateBlocklist();
// 	simulateBlocklistIntegration();
// }
