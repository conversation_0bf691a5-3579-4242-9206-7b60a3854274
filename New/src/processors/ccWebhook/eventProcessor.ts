/**
 * CliniCore Webhook Event Processing
 *
 * Main event processing orchestrator for CliniCore webhook events.
 * Handles event filtering, validation, patient synchronization coordination,
 * and comprehensive error handling with detailed logging and statistics.
 *
 * @fileoverview Main event processing logic for CC webhook events
 * @version 1.0.0
 * @since 2024-07-27
 */

import getConfig from "@/utils/configs";
import { logDatabaseError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { mapCcPatientToApContact } from "./fieldMapper";
import {
	checkSyncBuffer,
	createPatientRecord,
	lookupExistingPatient,
	updatePatientRecord,
	upsertApContact,
} from "./patientSynchronizer";
import type {
	CCPatientWebhookPayload,
	CCWebhookPayload,
	EventProcessingContext,
	EventProcessingResult,
	PatientSelect,
	PatientSyncResult,
	WebhookProcessingConfig,
} from "./types";

/**
 * Process CliniCore webhook event
 *
 * Main entry point for processing CliniCore webhook events. Handles event
 * filtering, validation, and delegates to appropriate processors based on
 * the event type and model.
 *
 * **Processing Flow:**
 * 1. Validate and filter webhook payload
 * 2. Extract event metadata and context
 * 3. Route to appropriate processor (patient, appointment, etc.)
 * 4. Handle errors and generate comprehensive results
 * 5. Log processing statistics and performance metrics
 *
 * @param payload - CliniCore webhook payload
 * @param requestId - Request ID for tracing and correlation
 * @param config - Optional processing configuration overrides
 * @returns Complete event processing result with success/error details
 *
 * @example
 * ```typescript
 * const result = await processWebhookEvent(webhookPayload, "req-123");
 * if (result.success) {
 *   console.log(`Processed ${result.model} ${result.event} in ${result.metadata.durationMs}ms`);
 * } else {
 *   console.error(`Processing failed: ${result.error?.message}`);
 * }
 * ```
 */
export async function processWebhookEvent(
	payload: CCWebhookPayload,
	requestId: string,
	config?: Partial<WebhookProcessingConfig>,
): Promise<EventProcessingResult> {
	const startTime = new Date();

	logInfo(
		`Processing CC webhook event: ${payload.event} ${payload.model} ID:${payload.id}`,
	);

	try {
		// Validate and filter event
		const filterResult = filterWebhookEvent(payload, requestId);
		if (!filterResult.shouldProcess) {
			return createSkippedResult(
				payload,
				startTime,
				requestId,
				filterResult.reason,
			);
		}

		// Create processing context
		const context = createProcessingContext(payload, requestId, config);

		// Route to appropriate processor
		let processingResult: EventProcessingResult;

		if (payload.model === "Patient") {
			processingResult = await processPatientEvent(
				payload as CCPatientWebhookPayload,
				context,
			);
		} else {
			// Future: Add processors for other models (Appointment, CustomField, etc.)
			processingResult = createUnsupportedModelResult(
				payload,
				startTime,
				requestId,
			);
		}

		return processingResult;
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"webhook_event_processing",
		);

		logError(
			`Unexpected error processing webhook event: ${error}`,
		);

		return createErrorResult(
			payload,
			startTime,
			requestId,
			error instanceof Error ? error.message : String(error),
			"validation",
		);
	}
}

/**
 * Filter webhook events to determine if they should be processed
 *
 * @param payload - Webhook payload to filter
 * @param requestId - Request ID for logging
 * @returns Filter result with processing decision and reason
 */
function filterWebhookEvent(
	payload: CCWebhookPayload,
	requestId: string,
): { shouldProcess: boolean; reason: string } {
	// Check event type
	const supportedEvents = ["EntityWasCreated", "EntityWasUpdated"];
	if (!supportedEvents.includes(payload.event)) {
		logInfo(`Skipping unsupported event type: ${payload.event}`);
		return {
			shouldProcess: false,
			reason: `Unsupported event type: ${payload.event}`,
		};
	}

	// Check model type
	const supportedModels = ["Patient"]; // Future: Add "Appointment", "CustomField", etc.
	if (!supportedModels.includes(payload.model)) {
		logInfo(`Skipping unsupported model type: ${payload.model}`);
		return {
			shouldProcess: false,
			reason: `Unsupported model type: ${payload.model}`,
		};
	}

	// Validate payload structure
	if (!payload.payload || typeof payload.payload !== "object") {
		logWarn(
			`Invalid payload structure for ${payload.model} ${payload.event}`,
		);
		return {
			shouldProcess: false,
			reason: "Invalid payload structure",
		};
	}

	logDebug(
		`Event passed filtering: ${payload.event} ${payload.model}`,
	);
	return { shouldProcess: true, reason: "Event passed all filters" };
}

/**
 * Create processing context from webhook payload and configuration
 *
 * @param payload - Webhook payload
 * @param requestId - Request ID
 * @param config - Optional configuration overrides
 * @returns Processing context with metadata and configuration
 */
function createProcessingContext(
	payload: CCWebhookPayload,
	requestId: string,
	config?: Partial<WebhookProcessingConfig>,
): EventProcessingContext {
	const defaultConfig = {
		syncBufferTimeSec: getConfig("syncBufferTimeSec") as number,
		maxProcessingTimeMs: 25000, // 25 seconds for webhook timeout
		enableDetailedLogging: true,
	};

	return {
		requestId,
		payload,
		processedAt: new Date(),
		syncBufferTimeSec:
			config?.syncBufferTimeSec ?? defaultConfig.syncBufferTimeSec,
	};
}

/**
 * Process patient-specific webhook events
 *
 * Handles patient creation and update events with complete synchronization
 * to AutoPatient, including field mapping, database operations, and error handling.
 *
 * @param payload - Patient webhook payload
 * @param context - Processing context
 * @returns Patient processing result with sync details
 */
async function processPatientEvent(
	payload: CCPatientWebhookPayload,
	context: EventProcessingContext,
): Promise<EventProcessingResult> {
	const startTime = new Date();
	const { requestId } = context;
	const ccPatient = payload.payload;

	logDebug(
		`Processing patient event for CC ID: ${ccPatient.id}`,
	);

	try {
		// Step 1: Look up existing patient record
		const lookupResult = await lookupExistingPatient(
			ccPatient.id,
			ccPatient.email,
			ccPatient.phoneMobile || undefined,
			requestId,
		);

		// Step 2: Check sync buffer if patient exists
		if (lookupResult.found && lookupResult.patient) {
			const bufferCheck = checkSyncBuffer(
				ccPatient.updatedAt,
				lookupResult.patient.ccUpdatedAt,
				context.syncBufferTimeSec,
				requestId,
			);

			if (!bufferCheck.shouldProcess) {
				return createSkippedResult(
					payload,
					startTime,
					requestId,
					bufferCheck.reason,
				);
			}
		}

		// Step 3: Map CC patient fields to AP contact format
		const fieldMapping = mapCcPatientToApContact(ccPatient, requestId);

		// Step 4: Upsert contact in AutoPatient
		const existingApId = lookupResult.patient?.apId || undefined;
		const apResponse = await upsertApContact(
			fieldMapping.apContactData,
			existingApId,
			requestId,
		);

		if (!apResponse.success || !apResponse.contact) {
			return createErrorResult(
				payload,
				startTime,
				requestId,
				apResponse.error || "Failed to upsert AP contact",
				"api_call",
			);
		}

		// Step 5: Update database record
		let updatedPatient: PatientSelect | null;
		if (lookupResult.found && lookupResult.patient) {
			// Update existing patient
			updatedPatient = await updatePatientRecord(
				lookupResult.patient.id,
				ccPatient,
				apResponse.contact,
				requestId,
			);
		} else {
			// Create new patient record
			updatedPatient = await createPatientRecord(
				ccPatient,
				apResponse.contact,
				requestId,
			);
		}

		if (!updatedPatient) {
			return createErrorResult(
				payload,
				startTime,
				requestId,
				"Failed to update database record",
				"database",
			);
		}

		// Create successful result
		const endTime = new Date();
		const patientSyncResult: PatientSyncResult = {
			success: true,
			action: lookupResult.found ? "updated" : "created",
			patient: updatedPatient,
			apContact: apResponse.contact,
			fieldMapping,
			stats: {
				processingTimeMs: endTime.getTime() - startTime.getTime(),
				fieldsMapped: fieldMapping.mappedStandardFields.length,
				apiCalls: 1,
			},
		};

		logInfo(
			`Successfully ${patientSyncResult.action} patient: ` +
				`CC:${ccPatient.id} -> AP:${apResponse.contact.id}`,
		);

		return {
			success: true,
			event: payload.event,
			model: payload.model,
			entityId: payload.id,
			patientSync: patientSyncResult,
			metadata: {
				requestId,
				startTime,
				endTime,
				durationMs: endTime.getTime() - startTime.getTime(),
			},
		};
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_event_processing",
		);

		return createErrorResult(
			payload,
			startTime,
			requestId,
			error instanceof Error ? error.message : String(error),
			"sync_check",
		);
	}
}

/**
 * Create a skipped result for events that don't need processing
 */
function createSkippedResult(
	payload: CCWebhookPayload,
	startTime: Date,
	requestId: string,
	_reason: string,
): EventProcessingResult {
	const endTime = new Date();

	return {
		success: true,
		event: payload.event,
		model: payload.model,
		entityId: payload.id,
		patientSync: {
			success: true,
			action: "skipped",
			stats: {
				processingTimeMs: endTime.getTime() - startTime.getTime(),
				fieldsMapped: 0,
				apiCalls: 0,
			},
		},
		metadata: {
			requestId,
			startTime,
			endTime,
			durationMs: endTime.getTime() - startTime.getTime(),
		},
	};
}

/**
 * Create an error result for failed processing
 */
function createErrorResult(
	payload: CCWebhookPayload,
	startTime: Date,
	requestId: string,
	message: string,
	stage:
		| "validation"
		| "lookup"
		| "sync_check"
		| "mapping"
		| "api_call"
		| "database",
): EventProcessingResult {
	const endTime = new Date();

	return {
		success: false,
		event: payload.event,
		model: payload.model,
		entityId: payload.id,
		metadata: {
			requestId,
			startTime,
			endTime,
			durationMs: endTime.getTime() - startTime.getTime(),
		},
		error: {
			message,
			type: "processing_error",
			stage,
		},
	};
}

/**
 * Create result for unsupported model types
 */
function createUnsupportedModelResult(
	payload: CCWebhookPayload,
	startTime: Date,
	requestId: string,
): EventProcessingResult {
	return createSkippedResult(
		payload,
		startTime,
		requestId,
		`Model ${payload.model} processing not yet implemented`,
	);
}
