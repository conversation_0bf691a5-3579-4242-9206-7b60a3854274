/**
 * CliniCore to AutoPatient Field Mapping
 *
 * Provides field mapping utilities for converting CliniCore patient data
 * to AutoPatient contact format. Handles standard field mappings, custom
 * field processing, and data type conversions with comprehensive logging
 * and error handling.
 *
 * @fileoverview Field mapping utilities for CC to AP patient synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { GetCCPatientType, PostAPContactType } from "@type";
import { findStandardFieldMapping } from "@/config/standardFieldMappings";
import { logDebug, logInfo, logWarn } from "@/utils/logger";
import type { FieldMappingResult } from "./types";

/**
 * Map CliniCore patient data to AutoPatient contact format
 *
 * Converts CliniCore patient fields to AutoPatient contact fields using
 * the standard field mapping configuration. Handles both direct field
 * mappings and custom field processing.
 *
 * **Mapping Strategy:**
 * 1. Map standard CC patient fields to AP contact fields
 * 2. Handle phone field variations (phoneMobile, phonePersonal, phoneBusiness)
 * 3. Process address information from CC addresses array
 * 4. Identify custom fields that need separate processing
 * 5. Apply data type conversions and validation
 *
 * @param ccPatient - CliniCore patient data from webhook payload
 * @param requestId - Request ID for logging and tracing
 * @returns Field mapping result with AP contact data and processing details
 *
 * @example
 * ```typescript
 * const mappingResult = mapCcPatientToApContact(ccPatient, "req-123");
 * console.log(`Mapped ${mappingResult.mappedStandardFields.length} standard fields`);
 *
 * // Use the mapped data for AP API call
 * const apContact = await apiClient.ap.createContact(mappingResult.apContactData);
 * ```
 */
export function mapCcPatientToApContact(
	ccPatient: GetCCPatientType,
	requestId: string,
): FieldMappingResult {
	logDebug(
		`Starting CC to AP field mapping for patient ID: ${ccPatient.id}`,
	);

	const apContactData: Partial<PostAPContactType> = {};
	const mappedStandardFields: string[] = [];
	const customFieldsToProcess: Array<{
		ccFieldName: string;
		ccFieldValue: unknown;
		apFieldName?: string;
	}> = [];
	const unmappedFields: string[] = [];

	// Map standard patient fields
	mapStandardFields(ccPatient, apContactData, mappedStandardFields, requestId);

	// Map phone fields with priority handling
	mapPhoneFields(ccPatient, apContactData, mappedStandardFields, requestId);

	// Map address fields from addresses array
	mapAddressFields(ccPatient, apContactData, mappedStandardFields, requestId);

	// Process custom fields and unmapped fields
	processCustomFields(
		ccPatient,
		customFieldsToProcess,
		unmappedFields,
		requestId,
	);

	logInfo(
		`Field mapping completed: ${mappedStandardFields.length} standard fields, ` +
			`${customFieldsToProcess.length} custom fields, ${unmappedFields.length} unmapped fields`,
	);

	logDebug(
		`Mapped standard fields: ${mappedStandardFields.join(", ")}`,
	);

	if (unmappedFields.length > 0) {
		logWarn(`Unmapped fields: ${unmappedFields.join(", ")}`);
	}

	return {
		apContactData,
		mappedStandardFields,
		customFieldsToProcess,
		unmappedFields,
	};
}

/**
 * Map standard CliniCore patient fields to AutoPatient contact fields
 *
 * @param ccPatient - CliniCore patient data
 * @param apContactData - AutoPatient contact data being built
 * @param mappedFields - Array to track mapped field names
 * @param requestId - Request ID for logging
 */
function mapStandardFields(
	ccPatient: GetCCPatientType,
	apContactData: Partial<PostAPContactType>,
	mappedFields: string[],
	requestId: string,
): void {
	// Direct field mappings
	const directMappings: Array<{
		ccField: keyof GetCCPatientType;
		apField: keyof PostAPContactType;
		transform?: (value: unknown) => unknown;
	}> = [
		{ ccField: "firstName", apField: "firstName" },
		{ ccField: "lastName", apField: "lastName" },
		{ ccField: "email", apField: "email" },
		{ ccField: "dob", apField: "dateOfBirth" },
		{ ccField: "ssn", apField: "ssn" },
		{ ccField: "gender", apField: "gender" },
	];

	for (const mapping of directMappings) {
		const ccValue = ccPatient[mapping.ccField];
		if (ccValue !== null && ccValue !== undefined && ccValue !== "") {
			const transformedValue = mapping.transform
				? mapping.transform(ccValue)
				: ccValue;
			(apContactData as Record<string, unknown>)[mapping.apField] =
				transformedValue;
			mappedFields.push(mapping.ccField);

			logDebug(
				`Mapped ${mapping.ccField} -> ${mapping.apField}: ${ccValue}`,
			);
		}
	}

	// Generate full name if not present
	if (ccPatient.firstName && ccPatient.lastName) {
		apContactData.name = `${ccPatient.firstName} ${ccPatient.lastName}`.trim();
		mappedFields.push("name");
		logDebug(`Generated name: ${apContactData.name}`);
	}
}

/**
 * Map phone fields with priority handling
 *
 * CliniCore has multiple phone fields (phoneMobile, phonePersonal, phoneBusiness).
 * AutoPatient has a single phone field. We prioritize mobile, then personal, then business.
 *
 * @param ccPatient - CliniCore patient data
 * @param apContactData - AutoPatient contact data being built
 * @param mappedFields - Array to track mapped field names
 * @param requestId - Request ID for logging
 */
function mapPhoneFields(
	ccPatient: GetCCPatientType,
	apContactData: Partial<PostAPContactType>,
	mappedFields: string[],
	requestId: string,
): void {
	const phoneFields = [
		{ field: "phoneMobile" as const, priority: 1 },
		{ field: "phonePersonal" as const, priority: 2 },
		{ field: "phoneBusiness" as const, priority: 3 },
	];

	for (const { field } of phoneFields) {
		const phoneValue = ccPatient[field];
		if (phoneValue && !apContactData.phone) {
			apContactData.phone = phoneValue;
			mappedFields.push(field);
			logDebug(`Mapped ${field} -> phone: ${phoneValue}`);
			break; // Use the first available phone number
		}
	}
}

/**
 * Map address fields from CliniCore addresses array
 *
 * CliniCore stores addresses in an array. We use the primary address
 * or the first address if no primary is marked.
 *
 * @param ccPatient - CliniCore patient data
 * @param apContactData - AutoPatient contact data being built
 * @param mappedFields - Array to track mapped field names
 * @param requestId - Request ID for logging
 */
function mapAddressFields(
	ccPatient: GetCCPatientType,
	apContactData: Partial<PostAPContactType>,
	mappedFields: string[],
	requestId: string,
): void {
	if (!ccPatient.addresses || ccPatient.addresses.length === 0) {
		return;
	}

	// Find primary address or use first address
	const primaryAddress = ccPatient.addresses.find((addr) => addr.primary === 1);
	const addressToUse = primaryAddress || ccPatient.addresses[0];

	if (addressToUse) {
		// Map address fields
		if (addressToUse.street) {
			const streetAddress = addressToUse.streetNumber
				? `${addressToUse.street} ${addressToUse.streetNumber}`.trim()
				: addressToUse.street;
			apContactData.address1 = streetAddress;
			mappedFields.push("address1");
			logDebug(`Mapped address -> address1: ${streetAddress}`);
		}

		if (addressToUse.city) {
			apContactData.city = addressToUse.city;
			mappedFields.push("city");
			logDebug(`Mapped city -> city: ${addressToUse.city}`);
		}

		if (addressToUse.postalCode) {
			apContactData.postalCode = addressToUse.postalCode;
			mappedFields.push("postalCode");
			logDebug(
				`Mapped postalCode -> postalCode: ${addressToUse.postalCode}`,
			);
		}

		if (addressToUse.country) {
			apContactData.country = addressToUse.country;
			mappedFields.push("country");
			logDebug(
				`Mapped country -> country: ${addressToUse.country}`,
			);
		}
	}
}

/**
 * Process custom fields and identify unmapped fields
 *
 * Identifies fields that should be processed as custom fields or
 * fields that cannot be mapped to AutoPatient.
 *
 * @param ccPatient - CliniCore patient data
 * @param customFieldsToProcess - Array to collect custom fields
 * @param unmappedFields - Array to collect unmapped field names
 * @param requestId - Request ID for logging
 */
function processCustomFields(
	ccPatient: GetCCPatientType,
	customFieldsToProcess: Array<{
		ccFieldName: string;
		ccFieldValue: unknown;
		apFieldName?: string;
	}>,
	_unmappedFields: string[],
	requestId: string,
): void {
	// Fields that are handled elsewhere or should be ignored
	const ignoredFields = new Set([
		"id",
		"createdAt",
		"updatedAt",
		"createdBy",
		"updatedBy",
		"firstName",
		"lastName",
		"email",
		"dob",
		"ssn",
		"gender",
		"phoneMobile",
		"phonePersonal",
		"phoneBusiness",
		"addresses",
		"categories",
		"customFields",
		"invoices",
		"payments",
		"files",
		"history",
		"appointments",
		"messages",
		"medications",
		"qrUrl",
		"avatarUrl",
		"active",
		"flashMessage",
	]);

	// Process remaining fields
	for (const [fieldName, fieldValue] of Object.entries(ccPatient)) {
		if (ignoredFields.has(fieldName)) {
			continue;
		}

		if (fieldValue === null || fieldValue === undefined || fieldValue === "") {
			continue;
		}

		// Check if this field has a standard field mapping
		const standardMapping = findStandardFieldMapping(fieldName, "cc", "ap");
		if (standardMapping) {
			customFieldsToProcess.push({
				ccFieldName: fieldName,
				ccFieldValue: fieldValue,
				apFieldName: standardMapping.targetField,
			});
			logDebug(
				`Found standard field mapping: ${fieldName} -> ${standardMapping.targetField}`,
			);
		} else {
			// This is a custom field or unmapped field
			customFieldsToProcess.push({
				ccFieldName: fieldName,
				ccFieldValue: fieldValue,
			});
			logDebug(`Identified custom field: ${fieldName}`);
		}
	}
}
