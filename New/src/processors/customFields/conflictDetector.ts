/**
 * Custom Fields Conflict Detection
 *
 * Provides comprehensive conflict detection for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Detects standard
 * field conflicts, existing custom field conflicts, and blocklist violations
 * to prevent duplicate field creation and maintain data integrity.
 *
 * @fileoverview Conflict detection utilities for custom field synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import {
	checkApToCcCreationBlocklist,
	findStandardFieldMapping,
	isStandardFieldConflict,
	type StandardFieldMapping,
} from "@/config/standardFieldMappings";
import { logInfo } from "@/utils/logger";
import { findExistingCustomField } from "./fieldMatcher";
import type { FieldConflictResult, Platform } from "./types";

/**
 * Check if a field should be skipped due to standard field conflict
 *
 * Determines whether a custom field creation should be skipped because
 * the field name conflicts with a standard field on the target platform
 * or maps to an existing standard field. This prevents creating custom
 * fields that would duplicate standard platform functionality.
 *
 * The function performs two types of checks:
 * 1. Standard field mapping - checks if the field maps to a standard field
 * 2. Standard field conflict - checks if the field name conflicts with standard fields
 *
 * @param fieldName - Field name to check for conflicts
 * @param sourcePlatform - Platform where this is a custom field ("ap" | "cc")
 * @param targetPlatform - Platform where we want to create the field ("ap" | "cc")
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Standard field mapping if found, null if field can be created safely
 *
 * @example
 * ```typescript
 * const mapping = checkForStandardFieldMapping(
 *   "email",
 *   "ap",
 *   "cc",
 *   "req-123"
 * );
 *
 * if (mapping) {
 *   console.log(`Field maps to standard field: ${mapping.targetField}`);
 *   // Skip creation and store mapping instead
 * } else {
 *   // Safe to create custom field
 * }
 * ```
 *
 * @since 1.0.0
 */
export function checkForStandardFieldMapping(
	fieldName: string,
	sourcePlatform: Platform,
	targetPlatform: Platform,
	requestId: string,
): StandardFieldMapping | null {
	// Check if this field maps to a standard field on the target platform
	const standardMapping = findStandardFieldMapping(
		fieldName,
		sourcePlatform,
		targetPlatform,
	);

	if (standardMapping) {
		logInfo("Found standard field mapping", {
			requestId,
			sourceField: fieldName,
			sourcePlatform,
			targetField: standardMapping.targetField,
			targetPlatform,
			notes: standardMapping.notes,
		});
		return standardMapping;
	}

	// Check if the field name conflicts with a standard field on the target platform
	if (isStandardFieldConflict(fieldName, targetPlatform)) {
		logInfo("Field name conflicts with standard field", {
			requestId,
			fieldName,
			targetPlatform,
			action: "skipping_creation",
		});

		// Create a mapping for this conflict
		return {
			sourceField: fieldName,
			targetField: fieldName,
			sourcePlatform,
			targetPlatform,
			notes: `Field name conflicts with ${targetPlatform.toUpperCase()} standard field`,
		};
	}

	return null;
}

/**
 * Check if a field conflicts with existing custom fields on the target platform
 *
 * This function prevents duplicate custom field creation by detecting when
 * a field name already exists on the target platform as a custom field.
 * It uses comprehensive field matching to identify potential conflicts.
 *
 * When a conflict is detected, the function logs detailed information about
 * the existing field and recommends creating a mapping instead of a duplicate.
 *
 * @param sourceField - Source custom field to check for conflicts
 * @param targetPlatformFields - Existing custom fields on target platform
 * @param targetPlatform - Target platform identifier ("ap" | "cc")
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Existing field if conflict found, null if field can be created safely
 *
 * @example
 * ```typescript
 * const existingField = checkForExistingCustomFieldConflict(
 *   apField,
 *   ccCustomFields,
 *   "cc",
 *   "req-123"
 * );
 *
 * if (existingField) {
 *   console.log(`Conflict with existing field: ${existingField.name}`);
 *   // Create mapping to existing field instead of duplicate
 * } else {
 *   // Safe to create new custom field
 * }
 * ```
 *
 * @since 1.0.0
 */
export function checkForExistingCustomFieldConflict(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatformFields: (APGetCustomFieldType | GetCCCustomField)[],
	targetPlatform: Platform,
	requestId: string,
): APGetCustomFieldType | GetCCCustomField | null {
	const existingField = findExistingCustomField(
		sourceField.name,
		targetPlatformFields,
		targetPlatform,
	);

	if (existingField) {
		logInfo("Found existing custom field conflict", {
			requestId,
			sourceFieldId: sourceField.id,
			sourceFieldName: sourceField.name,
			existingFieldId: existingField.id,
			existingFieldName: existingField.name,
			targetPlatform,
			action: "creating_mapping_instead_of_duplicate",
		});
		return existingField;
	}

	return null;
}

/**
 * Check if AP field creation is blocked by blocklist rules
 *
 * Determines whether an AutoPatient field should be blocked from creation
 * on the CliniCore platform based on configured blocklist rules. This
 * prevents creation of fields that are known to cause issues or conflicts.
 *
 * @param apField - AP field to check against blocklist
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Blocklist entry if field is blocked, null if creation is allowed
 *
 * @example
 * ```typescript
 * const blocklistEntry = checkApFieldCreationBlocklist(apField, "req-123");
 *
 * if (blocklistEntry) {
 *   console.log(`Field blocked: ${blocklistEntry.reason}`);
 *   // Skip field creation
 * } else {
 *   // Proceed with field creation
 * }
 * ```
 *
 * @since 1.0.0
 */
export function checkApFieldCreationBlocklist(
	apField: APGetCustomFieldType,
	requestId: string,
): { pattern: string; reason: string; isRegex: boolean } | null {
	const blocklistEntry = checkApToCcCreationBlocklist(apField.name);

	if (blocklistEntry) {
		logInfo("AP field blocked from CC creation", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			blocklistPattern: blocklistEntry.pattern,
			blocklistReason: blocklistEntry.reason,
			isRegex: blocklistEntry.isRegex,
		});
		return blocklistEntry;
	}

	return null;
}

/**
 * Comprehensive conflict detection for field creation
 *
 * Performs all conflict detection checks in a single function call,
 * providing a comprehensive analysis of potential conflicts before
 * attempting field creation. This includes standard field conflicts,
 * existing custom field conflicts, and blocklist violations.
 *
 * @param sourceField - Source field to check for conflicts
 * @param targetPlatformFields - Existing fields on target platform
 * @param sourcePlatform - Source platform identifier
 * @param targetPlatform - Target platform identifier
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Comprehensive conflict detection result
 *
 * @example
 * ```typescript
 * const conflictResult = detectFieldConflicts(
 *   apField,
 *   ccCustomFields,
 *   "ap",
 *   "cc",
 *   "req-123"
 * );
 *
 * if (conflictResult.hasConflict) {
 *   switch (conflictResult.conflictType) {
 *     case "standard_field":
 *       // Handle standard field mapping
 *       break;
 *     case "existing_custom_field":
 *       // Handle existing field mapping
 *       break;
 *     case "blocklist":
 *       // Handle blocklist violation
 *       break;
 *   }
 * } else {
 *   // Safe to create field
 * }
 * ```
 *
 * @since 1.0.0
 */
export function detectFieldConflicts(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatformFields: (APGetCustomFieldType | GetCCCustomField)[],
	sourcePlatform: Platform,
	targetPlatform: Platform,
	requestId: string,
): FieldConflictResult {
	// Check for standard field conflicts
	const standardMapping = checkForStandardFieldMapping(
		sourceField.name,
		sourcePlatform,
		targetPlatform,
		requestId,
	);

	if (standardMapping) {
		return {
			hasConflict: true,
			conflictType: "standard_field",
			standardMapping,
		};
	}

	// Check for existing custom field conflicts
	const existingField = checkForExistingCustomFieldConflict(
		sourceField,
		targetPlatformFields,
		targetPlatform,
		requestId,
	);

	if (existingField) {
		return {
			hasConflict: true,
			conflictType: "existing_custom_field",
			existingField,
		};
	}

	// Check for blocklist violations (AP to CC only)
	if (
		sourcePlatform === "ap" &&
		targetPlatform === "cc" &&
		"dataType" in sourceField
	) {
		const blocklistEntry = checkApFieldCreationBlocklist(
			sourceField as APGetCustomFieldType,
			requestId,
		);

		if (blocklistEntry) {
			return {
				hasConflict: true,
				conflictType: "blocklist",
				blocklistInfo: blocklistEntry,
			};
		}
	}

	// No conflicts detected
	return {
		hasConflict: false,
	};
}
