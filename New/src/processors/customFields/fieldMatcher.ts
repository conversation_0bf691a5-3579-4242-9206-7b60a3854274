/**
 * Custom Fields Matching Logic
 *
 * Provides intelligent field matching algorithms for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Implements multiple
 * matching strategies including exact name matching, fuzzy matching, and
 * field key extraction for comprehensive field identification.
 *
 * @fileoverview Field matching utilities for custom field synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { matchString } from "@/utils/matchString";
import type { Platform } from "./types";

/**
 * Determine if two custom fields match using comprehensive comparison
 *
 * Enhanced matching logic that compares fields by name, label, and fieldKey properties
 * using normalized string matching. Handles case differences, special characters, umlauts,
 * and provides multiple matching strategies for better field detection.
 *
 * The function implements a multi-strategy approach:
 * 1. Exact name matching (case-insensitive)
 * 2. AP name with CC label matching
 * 3. Fuzzy matching using existing matchString utility
 * 4. AP fieldKey extraction and matching
 *
 * @param apField - AutoPatient custom field object to match
 * @param ccField - CliniCore custom field object to match against
 * @returns True if fields are considered a match, false otherwise
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "Patient Allergies",
 *   dataType: "TEXT",
 *   fieldKey: "contact.allergien"
 * };
 *
 * const ccField: GetCCCustomField = {
 *   id: 456,
 *   name: "allergien",
 *   label: "Patient Allergies",
 *   type: "TEXT"
 * };
 *
 * const isMatch = fieldsMatch(apField, ccField); // true
 * ```
 *
 * @since 1.0.0
 */
export function fieldsMatch(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	// Strategy 1: Exact name matching (case-insensitive)
	if (apField.name.toLowerCase() === ccField.name.toLowerCase()) {
		return true;
	}

	// Strategy 2: AP name with CC label (case-insensitive)
	if (apField.name.toLowerCase() === ccField.label.toLowerCase()) {
		return true;
	}

	// Strategy 3: Fuzzy matching using existing matchString utility
	if (
		matchString(apField.name, ccField.name) ||
		matchString(apField.name, ccField.label)
	) {
		return true;
	}

	// Strategy 4: AP fieldKey matching (if fieldKey exists)
	if (apField.fieldKey) {
		// Extract field name from fieldKey (e.g., "contact.allergien" -> "allergien")
		const fieldKeyName = apField.fieldKey.split(".").pop() || apField.fieldKey;

		if (
			fieldKeyName.toLowerCase() === ccField.name.toLowerCase() ||
			fieldKeyName.toLowerCase() === ccField.label.toLowerCase() ||
			matchString(fieldKeyName, ccField.name) ||
			matchString(fieldKeyName, ccField.label)
		) {
			return true;
		}
	}

	return false;
}

/**
 * Find existing custom field by name in a list of fields
 *
 * Performs comprehensive field matching to find existing custom fields
 * that match the given name. This prevents duplicate field creation by
 * identifying fields that already exist on the target platform.
 *
 * The function implements multiple matching strategies:
 * 1. Exact name match (case-insensitive)
 * 2. CC label matching (for CC fields)
 * 3. AP fieldKey extraction and matching (for AP fields)
 * 4. Fuzzy matching as fallback
 * 5. CC label fuzzy matching (for CC fields)
 *
 * @param fieldName - Name to search for in the existing fields
 * @param existingFields - Array of existing custom fields to search in
 * @param platform - Platform type for context-aware matching logic
 * @returns Matching field if found, null if no match exists
 *
 * @example
 * ```typescript
 * const existingCcFields: GetCCCustomField[] = [
 *   { id: 1, name: "allergies", label: "Patient Allergies", type: "TEXT" },
 *   { id: 2, name: "notes", label: "Medical Notes", type: "TEXTAREA" }
 * ];
 *
 * const foundField = findExistingCustomField(
 *   "Patient Allergies",
 *   existingCcFields,
 *   "cc"
 * );
 * // Returns the allergies field due to label match
 * ```
 *
 * @since 1.0.0
 */
export function findExistingCustomField(
	fieldName: string,
	existingFields: (APGetCustomFieldType | GetCCCustomField)[],
	platform: Platform,
): APGetCustomFieldType | GetCCCustomField | null {
	for (const field of existingFields) {
		// Strategy 1: Exact name match (case-insensitive)
		if (field.name.toLowerCase() === fieldName.toLowerCase()) {
			return field;
		}

		// Strategy 2: For CC fields, check label as well
		if (platform === "cc" && "label" in field) {
			const ccField = field as GetCCCustomField;
			if (ccField.label.toLowerCase() === fieldName.toLowerCase()) {
				return field;
			}
		}

		// Strategy 3: For AP fields, check fieldKey
		if (platform === "ap" && "fieldKey" in field) {
			const apField = field as APGetCustomFieldType;
			if (apField.fieldKey) {
				const fieldKeyName =
					apField.fieldKey.split(".").pop() || apField.fieldKey;
				if (fieldKeyName.toLowerCase() === fieldName.toLowerCase()) {
					return field;
				}
			}
		}

		// Strategy 4: Fuzzy matching as fallback
		if (matchString(fieldName, field.name)) {
			return field;
		}

		// Strategy 5: For CC fields, fuzzy match against label
		if (platform === "cc" && "label" in field) {
			const ccField = field as GetCCCustomField;
			if (matchString(fieldName, ccField.label)) {
				return field;
			}
		}
	}

	return null;
}

/**
 * Find matching field between two field arrays
 *
 * Searches for a field in the target array that matches the source field
 * using the comprehensive fieldsMatch algorithm. This is useful for
 * bidirectional field matching operations.
 *
 * @param sourceField - Field to find a match for
 * @param targetFields - Array of fields to search in
 * @param sourcePlatform - Platform of the source field
 * @returns Matching field if found, null otherwise
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = { id: 1, name: "allergies", dataType: "TEXT" };
 * const ccFields: GetCCCustomField[] = [
 *   { id: 10, name: "allergien", label: "Allergies", type: "TEXT" }
 * ];
 *
 * const match = findMatchingField(apField, ccFields, "ap");
 * // Returns the CC field that matches the AP field
 * ```
 *
 * @since 1.0.0
 */
export function findMatchingField(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetFields: (APGetCustomFieldType | GetCCCustomField)[],
	sourcePlatform: Platform,
): APGetCustomFieldType | GetCCCustomField | null {
	if (sourcePlatform === "ap") {
		const apField = sourceField as APGetCustomFieldType;
		for (const targetField of targetFields) {
			const ccField = targetField as GetCCCustomField;
			if (fieldsMatch(apField, ccField)) {
				return ccField;
			}
		}
	} else {
		const ccField = sourceField as GetCCCustomField;
		for (const targetField of targetFields) {
			const apField = targetField as APGetCustomFieldType;
			if (fieldsMatch(apField, ccField)) {
				return apField;
			}
		}
	}

	return null;
}
