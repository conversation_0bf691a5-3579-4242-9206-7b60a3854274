/**
 * Custom Fields Synchronization Types
 *
 * Type definitions and interfaces for custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Provides comprehensive
 * type safety for field matching, database operations, and synchronization
 * response structures.
 *
 * @fileoverview Type definitions for custom field synchronization operations
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { dbSchema } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type { StandardFieldMapping } from "@/config/standardFieldMappings";

/**
 * Custom field synchronization response interface
 *
 * Comprehensive response structure containing detailed statistics, results,
 * and error information from custom field synchronization operations.
 * Provides complete visibility into the synchronization process including
 * matched fields, created fields, blocked fields, and error tracking.
 *
 * @interface CustomFieldSyncResponse
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const syncResult: CustomFieldSyncResponse = await synchronizeCustomFields(requestId);
 *
 * console.log(`Matched ${syncResult.matchedCount} field pairs`);
 * console.log(`Created ${syncResult.creationStatistics.totalCreated} new fields`);
 * console.log(`Blocked ${syncResult.creationStatistics.creationBlockedCount} fields`);
 *
 * if (syncResult.errors.length > 0) {
 *   console.error('Synchronization errors:', syncResult.errors);
 * }
 * ```
 */
export interface CustomFieldSyncResponse {
	/** Number of successfully matched field pairs between AP and CC */
	matchedCount: number;

	/** Number of successfully upserted database mapping records */
	upsertedCount: number;

	/** Array of AP fields that couldn't be matched to any CC field */
	unmatchedApFields: APGetCustomFieldType[];

	/** Array of CC fields that couldn't be matched to any AP field */
	unmatchedCcFields: GetCCCustomField[];

	/** Array of AP fields that were successfully created in CC platform */
	createdCcFields: GetCCCustomField[];

	/** Array of CC fields that were successfully created in AP platform */
	createdApFields: APGetCustomFieldType[];

	/** Array of AP fields that were blocked from CC creation due to blocklist rules */
	blockedApFields: APGetCustomFieldType[];

	/** Array of standard field mappings found during synchronization process */
	standardFieldMappings: StandardFieldMapping[];

	/** Comprehensive processing statistics for the synchronization operation */
	statistics: {
		/** Total number of AP custom fields processed */
		totalApFields: number;
		/** Total number of CC custom fields processed */
		totalCcFields: number;
		/** Total number of fields processed (AP + CC) */
		totalProcessed: number;
		/** Total number of successfully matched field pairs */
		totalMatched: number;
		/** Total number of unmatched fields (AP + CC) */
		totalUnmatched: number;
		/** Total number of standard field mappings identified */
		totalStandardMappings: number;
	};

	/** Field creation statistics tracking new field generation */
	creationStatistics: {
		/** Number of AP fields successfully created in CC platform */
		apFieldsCreatedInCc: number;
		/** Number of CC fields successfully created in AP platform */
		ccFieldsCreatedInAp: number;
		/** Total number of fields created across both platforms */
		totalCreated: number;
		/** Number of field creation operations that failed */
		creationErrors: number;
		/** Number of field creations skipped due to standard field conflicts */
		creationSkippedDueToStandardFields: number;
		/** Number of field creations blocked by blocklist rules */
		creationBlockedCount: number;
		/** Number of AP fields skipped from CC creation due to configuration settings */
		apFieldsSkippedInCcDueToConfig: number;
		/** Number of CC fields skipped from AP creation due to configuration settings */
		ccFieldsSkippedInApDueToConfig: number;
	};

	/** Array of general errors encountered during synchronization process */
	errors: string[];

	/** Array of specific errors encountered during field creation operations */
	creationErrors: string[];
}

/**
 * Database record type for custom field mappings
 *
 * Inferred type from the database schema for custom field mapping records.
 * Used for type-safe database operations when storing field mappings.
 *
 * @typedef CustomFieldInsert
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const mappingData: CustomFieldInsert = {
 *   apId: apField.id,
 *   ccId: ccField.id,
 *   name: apField.name,
 *   label: ccField.label,
 *   type: apField.dataType,
 *   apConfig: apField,
 *   ccConfig: ccField,
 *   mappingType: "custom_to_custom",
 *   apStandardField: null,
 *   ccStandardField: null,
 * };
 * ```
 */
export type CustomFieldInsert = typeof dbSchema.customFields.$inferInsert;

/**
 * Platform identifier type for field operations
 *
 * Represents the supported platforms in the synchronization system.
 * Used for type-safe platform identification in field operations.
 *
 * @typedef Platform
 * @since 1.0.0
 */
export type Platform = "ap" | "cc";

/**
 * Field matching strategy enumeration
 *
 * Defines the different strategies used for matching fields between platforms.
 * Helps track which matching strategy was successful for debugging and optimization.
 *
 * @enum FieldMatchStrategy
 * @since 1.0.0
 */
export enum FieldMatchStrategy {
	/** Exact name matching (case-insensitive) */
	EXACT_NAME = "exact_name",
	/** AP name matches CC label */
	NAME_TO_LABEL = "name_to_label",
	/** Fuzzy string matching using matchString utility */
	FUZZY_MATCH = "fuzzy_match",
	/** AP fieldKey extraction and matching */
	FIELD_KEY = "field_key",
}

/**
 * Field creation result interface
 *
 * Represents the result of a field creation operation, including
 * success status, created field data, and error information.
 *
 * @interface FieldCreationResult
 * @since 1.0.0
 */
export interface FieldCreationResult {
	/** Whether the field creation was successful */
	success: boolean;
	/** The created field data (if successful) */
	field?: APGetCustomFieldType | GetCCCustomField;
	/** Error message (if creation failed) */
	error?: string;
	/** Whether the failure was due to an existing field conflict */
	existingFieldConflict?: boolean;
}

/**
 * Field conflict detection result interface
 *
 * Represents the result of checking for field conflicts, including
 * conflict type and existing field information.
 *
 * @interface FieldConflictResult
 * @since 1.0.0
 */
export interface FieldConflictResult {
	/** Whether a conflict was detected */
	hasConflict: boolean;
	/** Type of conflict detected */
	conflictType?: "standard_field" | "existing_custom_field" | "blocklist";
	/** Existing field that conflicts (if applicable) */
	existingField?: APGetCustomFieldType | GetCCCustomField;
	/** Standard field mapping (if standard field conflict) */
	standardMapping?: StandardFieldMapping;
	/** Blocklist entry information (if blocklist conflict) */
	blocklistInfo?: {
		pattern: string;
		reason: string;
		isRegex: boolean;
	};
}
