/**
 * AutoPatient Webhook Processing Module
 *
 * Comprehensive AutoPatient webhook processing utilities for contact synchronization
 * and event handling. Provides complete webhook event processing, contact
 * synchronization, field mapping, and database operations with intelligent
 * algorithms and comprehensive error handling.
 *
 * **Core Modules:**
 * - `eventProcessor`: Main webhook event processing orchestrator
 * - `contactSynchronizer`: Contact synchronization operations and database management
 * - `fieldMapper`: AP to CC field mapping with standard field handling
 * - `types`: TypeScript interfaces and type definitions
 *
 * **Key Features:**
 * - Comprehensive webhook event filtering and validation
 * - Intelligent contact synchronization with conflict detection
 * - Bidirectional field mapping with custom field support
 * - Database operations with upsert logic and error handling
 * - Sync buffer management to prevent processing loops
 * - Detailed logging and statistics tracking
 * - Strict TypeScript compliance without `any` usage
 * - Performance-optimized for production environments
 *
 * **Processing Flow:**
 * 1. Webhook payload validation and parsing
 * 2. Event filtering and routing to appropriate processors
 * 3. Database lookup for existing patient records
 * 4. Sync buffer checks to prevent duplicate processing
 * 5. Field mapping from AP contact to CC patient format
 * 6. CliniCore API integration for patient creation/updates
 * 7. Database record updates with sync timestamps
 * 8. Comprehensive result reporting with performance metrics
 *
 * **Supported Events:**
 * - `contact_created`: Creates new patient in CC and local database
 * - Future: `contact_updated`, `contact_deleted`
 *
 * **Error Handling:**
 * - Comprehensive error logging with request ID correlation
 * - Database error logging with automatic cleanup
 * - API error handling with retry logic (future enhancement)
 * - Graceful degradation for non-critical failures
 *
 * @example
 * ```typescript
 * import { processContactWebhookEvent } from '@processors/apWebhook';
 *
 * // Process an AutoPatient contact webhook event
 * const result = await processContactWebhookEvent(webhookPayload, "req-123");
 *
 * if (result.success) {
 *   console.log(`Processed contact ${result.contactId} in ${result.metadata.durationMs}ms`);
 *   if (result.contactSync) {
 *     console.log(`Contact ${result.contactSync.action}: AP:${result.contactId} -> CC:${result.contactSync.ccPatient?.id}`);
 *   }
 * } else {
 *   console.error(`Processing failed at ${result.error?.stage}: ${result.error?.message}`);
 * }
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

// Main event processing functionality
export { processContactWebhookEvent } from "./eventProcessor";

// Field mapping utilities
export { mapApContactToCcPatient } from "./fieldMapper";

// Contact synchronization utilities
export {
	checkSyncBuffer,
	createPatientRecord,
	lookupExistingPatient,
	updatePatientRecord,
	upsertCcPatient,
} from "./contactSynchronizer";

// Type definitions
export type {
	APContactCreationWebhookPayload,
	APWebhookEvent,
	ContactLookupResult,
	ContactSyncResult,
	EventProcessingContext,
	EventProcessingResult,
	FieldMappingResult,
	PatientSelect,
	WebhookProcessingConfig,
} from "./types";
