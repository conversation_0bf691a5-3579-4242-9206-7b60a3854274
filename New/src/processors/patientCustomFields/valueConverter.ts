/**
 * Custom Field Value Converter
 *
 * Provides bidirectional value conversion utilities for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Handles intelligent type
 * mapping, value transformation, and graceful error handling for all supported
 * field types with comprehensive logging and request ID correlation.
 *
 * **Key Conversion Rules:**
 * - <PERSON> boolean ↔ AP RADIO (Yes/No options)
 * - <PERSON> select (multiple) ↔ AP MULTIPLE_OPTIONS (comma-separated)
 * - <PERSON> select (single) ↔ AP RADIO (option values)
 * - CC text/textarea/email/telephone ↔ AP TEXT
 * - Graceful fallbacks for unmappable types
 *
 * @fileoverview Bidirectional custom field value conversion utilities
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logWarn, logError } from "@/utils/logger";
import type {
	ValueConversionContext,
	ValueConversionResult
} from "./types";

/**
 * Convert custom field value between platforms
 *
 * Main entry point for bidirectional custom field value conversion.
 * Automatically determines conversion direction and applies appropriate
 * transformation logic based on field types and platform context.
 *
 * @param value - Source value to convert
 * @param context - Conversion context with field definitions and metadata
 * @returns Promise resolving to conversion result with success status and converted value
 *
 * @example
 * ```typescript
 * const context: ValueConversionContext = {
 *   sourceField: apRadioField,
 *   targetField: ccBooleanField,
 *   sourcePlatform: "ap",
 *   targetPlatform: "cc",
 *   requestId: "req-123"
 * };
 *
 * const result = await convertFieldValue("Yes", context);
 * if (result.success) {
 *   console.log("Converted value:", result.convertedValue); // true
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function convertFieldValue(
	value: string | number | boolean | string[] | null,
	context: ValueConversionContext,
): Promise<ValueConversionResult> {
	const { sourceField, targetField, sourcePlatform, targetPlatform, requestId } = context;

	// Handle null/undefined values
	if (value === null || value === undefined) {
		logDebug("Converting null/undefined value", {
			requestId,
			sourceFieldId: getFieldId(sourceField),
			targetFieldId: getFieldId(targetField),
		});
		return { success: true, convertedValue: null };
	}

	try {
		// Determine conversion direction and apply appropriate logic
		if (sourcePlatform === "ap" && targetPlatform === "cc") {
			return await convertApToCcValue(value, sourceField as APGetCustomFieldType, targetField as GetCCCustomField, requestId);
		} else if (sourcePlatform === "cc" && targetPlatform === "ap") {
			return await convertCcToApValue(value, sourceField as GetCCCustomField, targetField as APGetCustomFieldType, requestId);
		} else {
			const error = `Invalid conversion direction: ${sourcePlatform} -> ${targetPlatform}`;
			logError(error, { requestId, sourcePlatform, targetPlatform });
			return { success: false, error };
		}
	} catch (error) {
		const errorMessage = `Value conversion failed: ${String(error)}`;
		logError(errorMessage, {
			requestId,
			sourceFieldId: getFieldId(sourceField),
			targetFieldId: getFieldId(targetField),
			sourceValue: value,
			error,
		});
		return { success: false, error: errorMessage };
	}
}

/**
 * Convert AutoPatient value to CliniCore format
 *
 * Handles conversion from AP field values to CC field values based on
 * field type mappings and value transformation rules.
 *
 * @param value - AP field value to convert
 * @param apField - AP field definition
 * @param ccField - CC field definition
 * @param requestId - Request ID for logging
 * @returns Promise resolving to conversion result
 *
 * @since 1.0.0
 */
async function convertApToCcValue(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): Promise<ValueConversionResult> {
	logDebug("Converting AP to CC value", {
		requestId,
		apFieldId: apField.id,
		apFieldType: apField.dataType,
		ccFieldId: ccField.id,
		ccFieldType: ccField.type,
		sourceValue: value,
	});

	// Handle AP RADIO to CC boolean conversion
	if (apField.dataType === "RADIO" && ccField.type === "boolean") {
		return convertApRadioToCcBoolean(value, apField, requestId);
	}

	// Handle AP RADIO to CC select conversion
	if (apField.dataType === "RADIO" && ccField.type === "select") {
		return convertApRadioToCcSelect(value, apField, ccField, requestId);
	}

	// Handle AP MULTIPLE_OPTIONS to CC select conversion
	if (apField.dataType === "MULTIPLE_OPTIONS" && ccField.type === "select") {
		return convertApMultipleOptionsToCcSelect(value, apField, ccField, requestId);
	}

	// Handle AP TEXT to CC text-based fields
	if (apField.dataType === "TEXT" && isTextBasedCcField(ccField.type)) {
		return convertApTextToCcText(value, requestId);
	}

	// Fallback: convert to string for text fields, or pass through for compatible types
	if (isTextBasedCcField(ccField.type)) {
		const convertedValue = String(value);
		logWarn("AP→CC fallback conversion to text", {
			requestId,
			apFieldType: apField.dataType,
			ccFieldType: ccField.type,
			originalValue: value,
			convertedValue,
		});
		return { success: true, convertedValue };
	}

	// Unsupported conversion
	const error = `Unsupported AP→CC conversion: ${apField.dataType} → ${ccField.type}`;
	logWarn(error, {
		requestId,
		apFieldId: apField.id,
		ccFieldId: ccField.id,
	});
	return { success: false, error };
}

/**
 * Convert CliniCore value to AutoPatient format
 *
 * Handles conversion from CC field values to AP field values based on
 * field type mappings and value transformation rules.
 *
 * @param value - CC field value to convert
 * @param ccField - CC field definition
 * @param apField - AP field definition
 * @param requestId - Request ID for logging
 * @returns Promise resolving to conversion result
 *
 * @since 1.0.0
 */
async function convertCcToApValue(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): Promise<ValueConversionResult> {
	logDebug("Converting CC to AP value", {
		requestId,
		ccFieldId: ccField.id,
		ccFieldType: ccField.type,
		apFieldId: apField.id,
		apFieldType: apField.dataType,
		sourceValue: value,
	});

	// Handle CC boolean to AP RADIO conversion
	if (ccField.type === "boolean" && apField.dataType === "RADIO") {
		return convertCcBooleanToApRadio(value, apField, requestId);
	}

	// Handle CC select to AP RADIO conversion
	if (ccField.type === "select" && apField.dataType === "RADIO") {
		return convertCcSelectToApRadio(value, ccField, apField, requestId);
	}

	// Handle CC select to AP MULTIPLE_OPTIONS conversion
	if (ccField.type === "select" && apField.dataType === "MULTIPLE_OPTIONS") {
		return convertCcSelectToApMultipleOptions(value, ccField, apField, requestId);
	}

	// Handle CC text-based fields to AP TEXT
	if (isTextBasedCcField(ccField.type) && apField.dataType === "TEXT") {
		return convertCcTextToApText(value, requestId);
	}

	// Fallback: convert to string for AP TEXT fields
	if (apField.dataType === "TEXT") {
		const convertedValue = String(value);
		logWarn("CC→AP fallback conversion to text", {
			requestId,
			ccFieldType: ccField.type,
			apFieldType: apField.dataType,
			originalValue: value,
			convertedValue,
		});
		return { success: true, convertedValue };
	}

	// Unsupported conversion
	const error = `Unsupported CC→AP conversion: ${ccField.type} → ${apField.dataType}`;
	logWarn(error, {
		requestId,
		ccFieldId: ccField.id,
		apFieldId: apField.id,
	});
	return { success: false, error };
}

/**
 * Convert AP RADIO value to CC boolean
 */
function convertApRadioToCcBoolean(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	const stringValue = String(value).toLowerCase().trim();
	
	// Common positive values
	const positiveValues = ["yes", "true", "1", "on", "enabled", "active"];
	// Common negative values  
	const negativeValues = ["no", "false", "0", "off", "disabled", "inactive"];

	if (positiveValues.includes(stringValue)) {
		logDebug("AP RADIO → CC boolean: true", { requestId, originalValue: value });
		return { success: true, convertedValue: true };
	}
	
	if (negativeValues.includes(stringValue)) {
		logDebug("AP RADIO → CC boolean: false", { requestId, originalValue: value });
		return { success: true, convertedValue: false };
	}

	// Fallback: check against AP field options if available
	if (apField.picklistOptions && apField.picklistOptions.length >= 2) {
		const firstOption = apField.picklistOptions[0].toLowerCase();
		const isFirstOption = stringValue === firstOption;
		logDebug("AP RADIO → CC boolean: option-based", {
			requestId,
			originalValue: value,
			convertedValue: isFirstOption,
			firstOption,
		});
		return { success: true, convertedValue: isFirstOption };
	}

	// Default to false for unknown values
	logWarn("AP RADIO → CC boolean: defaulting to false", {
		requestId,
		originalValue: value,
		reason: "Unknown value",
	});
	return { success: true, convertedValue: false };
}

/**
 * Convert CC boolean value to AP RADIO
 */
function convertCcBooleanToApRadio(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	const boolValue = Boolean(value);

	// Use AP field options if available
	if (apField.picklistOptions && apField.picklistOptions.length >= 2) {
		const convertedValue = boolValue ? apField.picklistOptions[0] : apField.picklistOptions[1];
		logDebug("CC boolean → AP RADIO: option-based", {
			requestId,
			originalValue: value,
			convertedValue,
			options: apField.picklistOptions,
		});
		return { success: true, convertedValue };
	}

	// Default Yes/No mapping
	const convertedValue = boolValue ? "Yes" : "No";
	logDebug("CC boolean → AP RADIO: default Yes/No", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Convert AP RADIO value to CC select
 */
function convertApRadioToCcSelect(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): ValueConversionResult {
	const stringValue = String(value);

	// For single-select CC fields, return the value as-is
	if (!ccField.allowMultipleValues) {
		logDebug("AP RADIO → CC select (single)", {
			requestId,
			originalValue: value,
			convertedValue: stringValue,
		});
		return { success: true, convertedValue: stringValue };
	}

	// For multi-select CC fields, return as single-item array
	const convertedValue = [stringValue];
	logDebug("AP RADIO → CC select (multiple)", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Convert CC select value to AP RADIO
 */
function convertCcSelectToApRadio(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	let stringValue: string;

	if (Array.isArray(value)) {
		// Take first value from array
		stringValue = value.length > 0 ? String(value[0]) : "";
		logDebug("CC select → AP RADIO: array to first value", {
			requestId,
			originalValue: value,
			convertedValue: stringValue,
		});
	} else {
		stringValue = String(value);
		logDebug("CC select → AP RADIO: direct conversion", {
			requestId,
			originalValue: value,
			convertedValue: stringValue,
		});
	}

	return { success: true, convertedValue: stringValue };
}

/**
 * Convert AP MULTIPLE_OPTIONS to CC select
 */
function convertApMultipleOptionsToCcSelect(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): ValueConversionResult {
	const stringValue = String(value);

	// Split comma-separated values
	const values = stringValue.split(",").map(v => v.trim()).filter(v => v.length > 0);

	if (!ccField.allowMultipleValues && values.length > 1) {
		// CC field doesn't allow multiple values, take first one
		const convertedValue = values[0];
		logWarn("AP MULTIPLE_OPTIONS → CC select: truncated to single value", {
			requestId,
			originalValue: value,
			convertedValue,
			reason: "Target field doesn't allow multiple values",
		});
		return { success: true, convertedValue };
	}

	const convertedValue = ccField.allowMultipleValues ? values : values[0] || "";
	logDebug("AP MULTIPLE_OPTIONS → CC select", {
		requestId,
		originalValue: value,
		convertedValue,
		allowMultiple: ccField.allowMultipleValues,
	});
	return { success: true, convertedValue };
}

/**
 * Convert CC select to AP MULTIPLE_OPTIONS
 */
function convertCcSelectToApMultipleOptions(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	let values: string[];

	if (Array.isArray(value)) {
		values = value.map(v => String(v));
	} else {
		values = [String(value)];
	}

	const convertedValue = values.join(",");
	logDebug("CC select → AP MULTIPLE_OPTIONS", {
		requestId,
		originalValue: value,
		convertedValue,
		valueCount: values.length,
	});
	return { success: true, convertedValue };
}

/**
 * Convert AP TEXT to CC text-based field
 */
function convertApTextToCcText(
	value: string | number | boolean | string[] | null,
	requestId: string,
): ValueConversionResult {
	const convertedValue = String(value);
	logDebug("AP TEXT → CC text", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Convert CC text-based field to AP TEXT
 */
function convertCcTextToApText(
	value: string | number | boolean | string[] | null,
	requestId: string,
): ValueConversionResult {
	const convertedValue = String(value);
	logDebug("CC text → AP TEXT", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Check if CC field type is text-based
 */
function isTextBasedCcField(fieldType: string): boolean {
	const textTypes = ["text", "textarea", "email", "telephone", "url"];
	return textTypes.includes(fieldType.toLowerCase());
}

/**
 * Get field ID from either platform field
 */
function getFieldId(field: APGetCustomFieldType | GetCCCustomField): string | number {
	return 'id' in field ? field.id : (field as GetCCCustomField).id;
}
