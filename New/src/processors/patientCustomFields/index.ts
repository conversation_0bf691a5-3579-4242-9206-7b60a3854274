/**
 * Patient Custom Fields Processor Module
 *
 * Comprehensive patient custom field value synchronization module providing
 * bidirectional synchronization between AutoPatient (AP) and CliniCore (CC)
 * platforms. This module handles value-level synchronization as opposed to
 * field definition synchronization.
 *
 * **Key Features:**
 * - Bidirectional custom field value synchronization (AP ↔ CC)
 * - Intelligent value conversion with type mapping
 * - Webhook loop prevention with timestamp management
 * - Comprehensive error handling and logging
 * - Request ID correlation for tracing
 * - Graceful handling of missing fields and mappings
 *
 * **Main Functions:**
 * - `syncApToCcCustomFields`: Sync values from AutoPatient to CliniCore
 * - `syncCcToApCustomFields`: Sync values from CliniCore to AutoPatient
 * - `convertFieldValue`: Convert values between platform formats
 *
 * **Usage Example:**
 * ```typescript
 * import { syncApToCcCustomFields, syncCcToApCustomFields } from '@processors/patientCustomFields';
 *
 * // Sync from AP to CC
 * const apToCcResult = await syncApToCcCustomFields("patient-123", "req-456", true);
 * if (apToCcResult.success) {
 *   console.log(`Synced ${apToCcResult.fieldsProcessed} fields to CC`);
 * }
 *
 * // Sync from CC to AP
 * const ccToApResult = await syncCcToApCustomFields("patient-123", "req-456", true);
 * if (ccToApResult.success) {
 *   console.log(`Synced ${ccToApResult.fieldsProcessed} fields to AP`);
 * }
 * ```
 *
 * @fileoverview Patient custom field value synchronization module
 * @version 1.0.0
 * @since 2024-07-27
 */

// Main synchronization functions
export {
	syncApToCcCustomFields,
	syncCcToApCustomFields,
} from "./valueSynchronizer";

// Value conversion utilities
export { convertFieldValue } from "./valueConverter";

// Type definitions
export type {
	SyncResult,
	Platform,
	PatientRecord,
	CustomFieldMapping,
	APCustomFieldValue,
	CCCustomFieldValue,
	ValueConversionContext,
	ValueConversionResult,
	FieldMappingPair,
	SyncOperationContext,
	RateLimitResult,
	APCustomFieldUpdatePayload,
	CCCustomFieldUpdatePayload,
} from "./types";
