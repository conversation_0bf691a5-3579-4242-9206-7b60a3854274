/**
 * Patient Custom Fields Value Synchronization Types
 *
 * Type definitions and interfaces for patient custom field value synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Provides comprehensive
 * type safety for value conversion, synchronization operations, and response
 * structures.
 *
 * @fileoverview Type definitions for patient custom field value synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { dbSchema } from "@database";
import type { APGetCustomFieldType, GetCCCustomField, GetCCPatientCustomField } from "@type";

/**
 * Platform identifier for synchronization operations
 *
 * Represents the target platform for synchronization operations.
 * Used for type-safe platform identification in sync methods.
 *
 * @typedef Platform
 * @since 1.0.0
 */
export type Platform = "ap" | "cc";

/**
 * Patient record type inferred from database schema
 *
 * Type-safe patient record structure for database operations.
 * Ensures compatibility with existing database schema.
 *
 * @typedef PatientRecord
 * @since 1.0.0
 */
export type PatientRecord = typeof dbSchema.patient.$inferSelect;

/**
 * Custom field mapping record type inferred from database schema
 *
 * Type-safe custom field mapping structure for database operations.
 * Used for retrieving field mappings between platforms.
 *
 * @typedef CustomFieldMapping
 * @since 1.0.0
 */
export type CustomFieldMapping = typeof dbSchema.customFields.$inferSelect;

/**
 * Synchronization result interface
 *
 * Comprehensive result structure for custom field value synchronization
 * operations. Provides success status, processing statistics, and error
 * information for detailed operation tracking.
 *
 * @interface SyncResult
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const result: SyncResult = await syncApToCcCustomFields(patientId, requestId);
 *
 * if (result.success) {
 *   console.log(`Successfully processed ${result.fieldsProcessed} fields`);
 * } else {
 *   console.error('Sync failed:', result.errors);
 * }
 * ```
 */
export interface SyncResult {
	/** Whether the synchronization operation was successful */
	success: boolean;
	/** Number of custom fields processed during synchronization */
	fieldsProcessed: number;
	/** Array of error messages encountered during synchronization */
	errors: string[];
}

/**
 * AutoPatient custom field value structure
 *
 * Represents a custom field value as stored in AutoPatient contacts.
 * Used for type-safe handling of AP custom field values.
 *
 * @interface APCustomFieldValue
 * @since 1.0.0
 */
export interface APCustomFieldValue {
	/** AutoPatient custom field ID */
	id: string;
	/** Custom field value (can be string, number, boolean, or null) */
	value: string | number | boolean | null;
}

/**
 * CliniCore custom field value structure
 *
 * Represents a custom field value as stored in CliniCore patients.
 * Used for type-safe handling of CC custom field values.
 *
 * @interface CCCustomFieldValue
 * @since 1.0.0
 */
export interface CCCustomFieldValue {
	/** CliniCore custom field ID */
	fieldId: number;
	/** Custom field value (can be string, number, boolean, array, or null) */
	value: string | number | boolean | string[] | null;
}

/**
 * Field value conversion context
 *
 * Provides context information for field value conversions including
 * source and target field definitions and conversion metadata.
 *
 * @interface ValueConversionContext
 * @since 1.0.0
 */
export interface ValueConversionContext {
	/** Source platform field definition */
	sourceField: APGetCustomFieldType | GetCCCustomField;
	/** Target platform field definition */
	targetField: APGetCustomFieldType | GetCCCustomField;
	/** Source platform identifier */
	sourcePlatform: Platform;
	/** Target platform identifier */
	targetPlatform: Platform;
	/** Request ID for logging correlation */
	requestId: string;
}

/**
 * Value conversion result interface
 *
 * Represents the result of a field value conversion operation including
 * success status, converted value, and error information.
 *
 * @interface ValueConversionResult
 * @since 1.0.0
 */
export interface ValueConversionResult {
	/** Whether the conversion was successful */
	success: boolean;
	/** Converted value (if successful) */
	convertedValue?: string | number | boolean | string[] | null;
	/** Error message (if conversion failed) */
	error?: string;
	/** Whether the conversion was skipped due to missing mapping */
	skipped?: boolean;
}

/**
 * Field mapping pair interface
 *
 * Represents a matched pair of custom fields between platforms with
 * their current values and mapping information.
 *
 * @interface FieldMappingPair
 * @since 1.0.0
 */
export interface FieldMappingPair {
	/** Database mapping record */
	mapping: CustomFieldMapping;
	/** AutoPatient field definition */
	apField: APGetCustomFieldType;
	/** CliniCore field definition */
	ccField: GetCCCustomField;
	/** Current value from source platform */
	sourceValue: string | number | boolean | string[] | null;
	/** Converted value for target platform */
	targetValue?: string | number | boolean | string[] | null;
}

/**
 * Synchronization operation context
 *
 * Provides context information for synchronization operations including
 * patient data, field mappings, and operation metadata.
 *
 * @interface SyncOperationContext
 * @since 1.0.0
 */
export interface SyncOperationContext {
	/** Patient database record */
	patient: PatientRecord;
	/** Source platform identifier */
	sourcePlatform: Platform;
	/** Target platform identifier */
	targetPlatform: Platform;
	/** Request ID for logging correlation */
	requestId: string;
	/** Whether to skip missing fields instead of failing */
	skipMissingFields: boolean;
	/** Whether this is a return callback from /cf endpoint */
	isReturnCallback: boolean;
}

/**
 * Rate limiting result interface
 *
 * Represents the result of rate limiting checks for the /cf endpoint.
 * Provides information about rate limit status and remaining quota.
 *
 * @interface RateLimitResult
 * @since 1.0.0
 */
export interface RateLimitResult {
	/** Whether the request is allowed (within rate limits) */
	allowed: boolean;
	/** Number of requests made in the current 24-hour window */
	requestCount: number;
	/** Maximum number of requests allowed per 24-hour window */
	maxRequests: number;
	/** Timestamp when the rate limit window resets */
	resetTime: Date;
	/** Number of seconds until the rate limit resets */
	resetInSeconds: number;
}

/**
 * Custom field value update payload for AutoPatient
 *
 * Represents the payload structure for updating custom field values
 * in AutoPatient contacts.
 *
 * @interface APCustomFieldUpdatePayload
 * @since 1.0.0
 */
export interface APCustomFieldUpdatePayload {
	/** Array of custom field values to update */
	customFields: APCustomFieldValue[];
}

/**
 * Custom field value update payload for CliniCore
 *
 * Represents the payload structure for updating custom field values
 * in CliniCore patients.
 *
 * @interface CCCustomFieldUpdatePayload
 * @since 1.0.0
 */
export interface CCCustomFieldUpdatePayload {
	/** Object mapping field IDs to values */
	customFields: Record<number, string | number | boolean | string[] | null>;
}
