export type GetAPContactType = {
	id: string;
	locationId: string;
	email?: string;
	phone?: string;
	name?: string;
	firstName?: string;
	lastName?: string;
	emailLowerCase?: string;
	timezone?: string;
	companyName?: string;
	dnd?: boolean;
	dndSettings?: {
		Call?: {
			status: string;
			message: string;
			code: string;
		};
		Email?: {
			status: string;
			message: string;
			code: string;
		};
		SMS?: {
			status: string;
			message: string;
			code: string;
		};
		WhatsApp?: {
			status: string;
			message: string;
			code: string;
		};
		GMB?: {
			status: string;
			message: string;
			code: string;
		};
		FB?: {
			status: string;
			message: string;
			code: string;
		};
	};
	type?: string;
	source?: string;
	assignedTo?: string;
	address1?: string;
	city?: string;
	state?: string;
	country?: string;
	postalCode?: string;
	website?: string;
	tags?: string[];
	dateOfBirth?: string;
	dateAdded: string;
	dateUpdated: string;
	attachments?: string;
	ssn?: string;
	gender?: string;
	keyword?: string;
	firstNameLowerCase?: string;
	fullNameLowerCase?: string;
	lastNameLowerCase?: string;
	lastActivity?: string;
	customFields?: { id: string; value: string }[];
	businessId?: string;
	attributionSource?: AttributionSourceType;
	lastAttributionSource?: AttributionSourceType;
};
export type PostAPContactType = {
	email?: string | null;
	phone?: string | null;
	name?: string | null;
	firstName?: string | null;
	lastName?: string | null;
	timezone?: string | null;
	dnd?: boolean;
	source?: string | null;
	assignedTo?: string | null;
	address1?: string | null;
	city?: string | null;
	state?: string | null;
	country?: string | null;
	postalCode?: string | null;
	tags?: string[];
	dateOfBirth?: string | null;
	ssn?: string | null;
	gender?: string | null;
	customFields?: { id: string; field_value: string | number }[];
};

export type GetAPAppointmentType = {
	calendarId: string;
	locationId: string;
	contactId: string;
	startTime: string;
	endTime: string;
	title: string | null;
	appointmentStatus?: string;
	appoinmentStatus?: string;
	assignedUserId?: string | null;
	address: string | null;
	id: string;
};

export type AttributionSourceType = {
	url?: string;
	campaign?: string;
	utmSource?: string;
	utmMedium?: string;
	utmContent?: string;
	referrer?: string;
	campaignId?: string;
	fbclid?: string;
	gclid?: string;
	msclikid?: string;
	dclid?: string;
	fbc?: string;
	fbp?: string;
	fbEventId?: string;
	userAgent?: string;
	ip?: string;
	medium?: string;
	mediumId?: string;
};

export type APGetCustomFieldType = {
	id: string;
	name: string;
	dataType: string;
	placeholder?: string;
	acceptedFormat?: string[];
	isMultipleFile?: boolean;
	maxNumberOfFiles?: number;
	textBoxListOptions?: {
		label: string;
		prefillValue: string;
	}[];
	position?: number;
	fieldKey?: string; // e.g., "contact.field_name", "custom_object.pet.name"
	picklistOptions?: string[]; // Options for RADIO, MULTIPLE_OPTIONS, SINGLE_OPTIONS fields
	isAllowedCustomOption?: boolean; // Whether custom options are allowed
};

export type APPostCustomfieldType = {
	name: string;
	dataType: string;
	model?: string;
	placeholder?: string;
	acceptedFormat?: string[];
	isMultipleFile?: boolean;
	maxNumberOfFiles?: number;
	textBoxListOptions?: {
		label: string;
		prefillValue: string;
	}[];
	// Official GoHighLevel API v2 uses "options" with {key, label} structure
	options?:
		| {
				key: string;
				label: string;
				url?: string; // Optional, valid only for RADIO type
		  }[]
		| string[]; // Location-specific endpoint actually expects simple string array
	position?: number;
	// V2 API additional fields
	locationId?: string;
	showInForms?: boolean;
	objectKey?: string; // e.g., "contact", "custom_object.pet"
	fieldKey?: string; // e.g., "contact.field_name", "custom_object.pet.name"
	description?: string;
	parentId?: string; // ID of parent folder
	allowCustomOption?: boolean; // For RADIO type fields
	maxFileLimit?: number; // For FILE_UPLOAD type
};

export type PostAPAppointmentType = {
	contactId: string;
	startTime: string;
	endTime?: string;
	title?: string;
	appointmentStatus?:
		| "new"
		| "confirmed"
		| "cancelled"
		| "showed"
		| "noshow"
		| "invalid";
	assignedUserId?: string;
	address?: string;
	ignoreDateRange?: boolean;
	toNotify?: boolean;
	ignoreFreeSlotValidation: boolean;
};

export type PutAPAppointmentType = {
	contactId?: string;
	startTime?: string;
	endTime?: string;
	title?: string;
	appointmentStatus?:
		| "new"
		| "confirmed"
		| "cancelled"
		| "showed"
		| "noshow"
		| "invalid";
	assignedUserId?: string;
	address?: string;
	ignoreDateRange?: boolean;
	ignoreFreeSlotValidation: boolean;
	toNotify?: boolean;
};

export type WebhookCalendar = {
	id: string;
	title: string;
	calendarName: string;
	selectedTimezone: string;
	appointmentId: string;
	startTime: string;
	endTime: string;
	status: string;
	appoinmentStatus: string;
	address: string;
	notes: string;
	date_created: string;
	created_by: string;
	created_by_user_id: string;
	created_by_meta: {
		source: string;
		channel: string;
	};
	last_updated_by_meta: {
		source: string;
		channel: string;
	};
};

/**
 * AutoPatient Webhook Location Information
 */
export type APWebhookLocation = {
	name: string;
	address: string;
	city: string;
	state: string;
	country: string;
	postalCode: string;
	fullAddress: string;
	id: string;
};

/**
 * AutoPatient Webhook Workflow Information
 */
export type APWebhookWorkflow = {
	id: string;
	name: string;
};

/**
 * AutoPatient Webhook Attribution Source
 */
export type APWebhookAttributionSource = {
	sessionSource?: string;
	medium?: string;
	mediumId?: string | null;
};

/**
 * AutoPatient Webhook Contact Information
 */
export type APWebhookContact = {
	attributionSource: APWebhookAttributionSource;
	lastAttributionSource: Record<string, unknown>;
};

/**
 * Base AutoPatient Webhook Payload
 *
 * Represents the common structure of AP webhook events.
 * Contains contact information and metadata about the webhook trigger.
 */
export type APWebhookPayload = {
	contact_id: string;
	first_name?: string;
	last_name?: string;
	full_name?: string;
	email?: string;
	phone?: string;
	tags?: string;
	country?: string;
	date_created: string;
	date_updated?: string;
	full_address?: string;
	contact_type?: string;
	location: APWebhookLocation;
	workflow: APWebhookWorkflow;
	triggerData: Record<string, unknown>;
	contact: APWebhookContact;
	attributionSource: Record<string, unknown>;
	customData: Record<string, unknown>;
	// Calendar property indicates appointment-related webhooks
	calendar?: WebhookCalendar;
	// Additional fields that may be present in webhook data
	[key: string]: unknown;
};

/**
 * AutoPatient Contact Webhook Payload
 *
 * Specific payload structure for contact creation and update events.
 * Excludes calendar property to distinguish from appointment webhooks.
 */
export type APContactWebhookPayload = Omit<APWebhookPayload, "calendar"> & {
	// Ensure calendar is not present for contact webhooks
	calendar?: never;
};

export type GetAPNoteType = {
	id: string;
	body: string;
	userId: string;
	dateAdded: string;
	contactId: string;
};

export type UpdateAPCustomfields = {
	name: string;
	value: string | number;
};
