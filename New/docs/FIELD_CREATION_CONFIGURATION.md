# Custom Field Creation Configuration

This document describes the configuration options that control automatic custom field creation during bidirectional synchronization between AutoPatient (AP) and CliniCore (CC) platforms.

## Overview

The synchronization system now provides fine-grained control over when custom fields are automatically created in either platform. This allows administrators to disable field creation while preserving all existing field matching, mapping, and synchronization functionality.

## Configuration Options

### `createMissingFieldsInCC`

**Type:** `boolean`  
**Default:** `true`  
**Location:** `New/src/utils/configs.ts`

Controls whether missing custom fields should be automatically created in CliniCore when they exist in AutoPatient but not in CC during AP→CC synchronization.

- **`true` (default):** AP fields that don't exist in CC will be automatically created in CC
- **`false`:** AP fields that don't exist in CC will be skipped for creation, but existing field matching continues

### `createMissingCustomFieldsInAP`

**Type:** `boolean`  
**Default:** `true`  
**Location:** `New/src/utils/configs.ts`

Controls whether missing custom fields should be automatically created in AutoPatient when they exist in CliniCore but not in AP during CC→AP synchronization.

- **`true` (default):** CC fields that don't exist in AP will be automatically created in AP
- **`false`:** CC fields that don't exist in AP will be skipped for creation, but existing field matching continues

## Implementation Details

### Configuration Location

```typescript
// New/src/utils/configs.ts
interface AppConfigs {
  // ... other config options
  
  /** Controls whether missing custom fields should be automatically created in CliniCore */
  createMissingFieldsInCC: boolean;
  
  /** Controls whether missing custom fields should be automatically created in AutoPatient */
  createMissingCustomFieldsInAP: boolean;
  
  // ... other config options
}

const configs: AppConfigs = {
  // ... other config values
  
  // Custom Field Synchronization Configuration (defaults to true for backward compatibility)
  createMissingFieldsInCC: true,
  createMissingCustomFieldsInAP: true,
  
  // ... other config values
};
```

### Synchronization Flow Impact

The configuration checks are implemented in `New/src/processors/customFields/fieldSynchronizer.ts`:

#### Phase 4: AP→CC Field Creation
```typescript
// After all conflict detection and blocklist checks
if (!getConfig("createMissingFieldsInCC")) {
  // Skip creation, increment statistics, log details
  response.creationStatistics.apFieldsSkippedInCcDueToConfig++;
  logDebug("Skipped AP field creation in CC due to configuration", {...});
  continue;
}

// Proceed with field creation
const createdCcField = await createApFieldInCc(apField, requestId);
```

#### Phase 5: CC→AP Field Creation
```typescript
// After all conflict detection checks
if (!getConfig("createMissingCustomFieldsInAP")) {
  // Skip creation, increment statistics, log details
  response.creationStatistics.ccFieldsSkippedInApDueToConfig++;
  logDebug("Skipped CC field creation in AP due to configuration", {...});
  continue;
}

// Proceed with field creation
const createdApField = await createCcFieldInAp(ccField, requestId);
```

## Statistics Tracking

New statistics fields have been added to track configuration-based field creation skipping:

```typescript
// New/src/processors/customFields/types.ts
interface CustomFieldSyncResponse {
  creationStatistics: {
    // ... existing fields
    
    /** Number of AP fields skipped from CC creation due to configuration settings */
    apFieldsSkippedInCcDueToConfig: number;
    
    /** Number of CC fields skipped from AP creation due to configuration settings */
    ccFieldsSkippedInApDueToConfig: number;
  };
}
```

These statistics are included in the API response from the custom fields handler.

## Preserved Functionality

When field creation is disabled, the following functionality continues to work normally:

✅ **Field Matching:** Existing fields are still matched between platforms  
✅ **Field Mapping:** Database mappings are created for matched fields  
✅ **Conflict Detection:** Standard field conflicts are still detected and handled  
✅ **Blocklist Filtering:** Blocklist rules are still applied  
✅ **Error Handling:** All existing error handling remains intact  
✅ **Logging:** All existing logging continues, plus new configuration-specific logs  
✅ **Statistics:** All existing statistics are preserved, plus new configuration tracking  

## Usage Scenarios

### Scenario 1: Prevent AP Fields from Being Created in CC
**Use Case:** CliniCore system is read-only or has strict field governance policies

```typescript
// In configs.ts
createMissingFieldsInCC: false,
createMissingCustomFieldsInAP: true, // Still allow CC→AP creation
```

**Result:** 
- AP fields won't be created in CC
- Existing AP↔CC field matching continues
- CC fields can still be created in AP

### Scenario 2: Prevent CC Fields from Being Created in AP
**Use Case:** AutoPatient system has limited custom field capacity

```typescript
// In configs.ts
createMissingFieldsInCC: true, // Still allow AP→CC creation
createMissingCustomFieldsInAP: false,
```

**Result:**
- CC fields won't be created in AP
- Existing AP↔CC field matching continues
- AP fields can still be created in CC

### Scenario 3: Disable All Automatic Field Creation
**Use Case:** Manual field management required for compliance

```typescript
// In configs.ts
createMissingFieldsInCC: false,
createMissingCustomFieldsInAP: false,
```

**Result:**
- No automatic field creation in either direction
- Only existing field matching and mapping occurs
- Full manual control over field creation

### Scenario 4: Default Behavior (Backward Compatible)
**Use Case:** Automatic field creation desired (current behavior)

```typescript
// In configs.ts (default values)
createMissingFieldsInCC: true,
createMissingCustomFieldsInAP: true,
```

**Result:**
- Full bidirectional field creation and synchronization
- Identical to previous system behavior

## Logging

When field creation is skipped due to configuration, DEBUG-level logs are generated:

```typescript
logDebug("Skipped AP field creation in CC due to configuration", {
  requestId,
  apFieldId: apField.id,
  apFieldName: apField.name,
  configSetting: "createMissingFieldsInCC",
  configValue: false,
  reason: "Field creation disabled by configuration",
});
```

## Backward Compatibility

This implementation is fully backward compatible:

- **Default Values:** Both configuration options default to `true`
- **Existing Behavior:** When both flags are `true`, the system behaves identically to the previous version
- **No Breaking Changes:** All existing APIs, interfaces, and functionality remain unchanged
- **Additive Implementation:** New functionality is added without modifying existing logic paths

## Testing

A comprehensive test suite is available in `New/src/test/configurationTest.ts` that validates:

- Default configuration values
- Configuration impact scenarios
- Statistics structure
- Backward compatibility
- Implementation details

Run the test with:
```bash
# Type checking (validates TypeScript compilation)
pnpm run type-check
```
