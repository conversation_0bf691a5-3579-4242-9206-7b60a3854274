# AutoPatient Custom Fields Parent Filtering

## Overview

This document describes the AutoPatient custom fields parent filtering capability that allows selective fetching of custom fields based on parent folder IDs. This feature provides fine-grained control over which custom fields are synchronized between AutoPatient and CliniCore systems.

## Configuration

### Configuration Property

The parent filtering is controlled by the `apCustomFieldsParents` property in the application configuration:

```typescript
interface AppConfigs {
  // ... other properties
  apCustomFieldsParents: string[];
  // ... other properties
}
```

### Configuration Location

The configuration is defined in `New/src/utils/configs.ts`:

```typescript
const configs: AppConfigs = {
  // ... other configurations
  apCustomFieldsParents: [], // Empty array = fetch all fields
  // ... other configurations
};
```

### Configuration Behavior

- **Empty Array (`[]`)**: Fetches ALL custom fields (default behavior, backward compatible)
- **Array with Parent IDs**: Fetches only custom fields under the specified parent IDs

## API Implementation

### New API Method

A new method `allWithParentFilter()` has been added to the AutoPatient API client:

```typescript
// Location: New/src/apiClient/apClient.ts
export const apCustomfield = {
  // ... existing methods
  
  /**
   * Get all custom fields with optional parent filtering
   */
  allWithParentFilter: async (invalidateCache?: boolean): Promise<APGetCustomFieldType[]>
};
```

### API Endpoint Details

The implementation uses different endpoints based on configuration:

#### When `apCustomFieldsParents` is empty:
- **Endpoint**: `https://services.leadconnectorhq.com/locations/{locationId}/customFields/?model=all`
- **Method**: GET
- **Behavior**: Returns all custom fields (same as existing `all()` method)

#### When `apCustomFieldsParents` contains parent IDs:
- **Endpoint**: `https://backend.leadconnectorhq.com/locations/{locationId}/customFields/search`
- **Method**: GET
- **Base URL**: Uses `https://backend.leadconnectorhq.com` (different from standard API domain)
- **Query Parameters**:
  - `parentId`: The parent ID to filter by
  - `documentType`: "field"
  - `model`: "all"
  - `includeStandards`: "true"
  - `skip`: "0"
  - `limit`: "1000"

### Response Format

The endpoints return different raw response structures, but both are converted to the same format:

#### Standard Endpoint Response:
```typescript
{
  customFields: APGetCustomFieldType[];
}
```

#### Search Endpoint Response (Raw):
```typescript
{
  customFields: BackendSearchCustomFieldType[];
  totalItems?: number;
  traceId?: string;
}
```

#### Converted Response:
Both endpoints ultimately return `APGetCustomFieldType[]` after proper conversion. The search endpoint response includes additional properties like `_id`, `parentId`, `documentType`, etc., which are mapped to the standard format while preserving the extended properties.

## Integration

### Custom Field Synchronization

The field synchronization process has been updated to use the new filtering method:

```typescript
// Location: New/src/processors/customFields/fieldSynchronizer.ts
const [apCustomFields, ccCustomFields, existingMappings] = await Promise.all([
  apiClient.ap.apCustomfield.allWithParentFilter(), // Updated to use parent filtering
  apiClient.cc.ccCustomfieldReq.all(),
  getDb().select().from(dbSchema.customFields),
]);
```

### Backward Compatibility

- The existing `all()` method remains unchanged
- v3Integration code continues to use the original `all()` method
- Default configuration (empty array) maintains current behavior

## Usage Examples

### Example 1: Fetch All Fields (Default)

```typescript
// Configuration
const configs = {
  apCustomFieldsParents: [] // Empty array
};

// Usage
const fields = await apCustomfield.allWithParentFilter();
// Returns all custom fields (same as apCustomfield.all())
```

### Example 2: Fetch Fields from Specific Parents

```typescript
// Configuration
const configs = {
  apCustomFieldsParents: ["RgQQpmZ5XV9dDm4hUsEX", "AnotherParentId"]
};

// Usage
const fields = await apCustomfield.allWithParentFilter();
// Returns only fields under the specified parent IDs
```

### Example 3: Testing the Configuration

```typescript
import { testParentFiltering } from "./test-parent-filtering";

// Run the test to verify filtering behavior
await testParentFiltering();
```

## Implementation Details

### API Endpoint Selection

The implementation uses different base URLs depending on the operation:

- **Standard operations**: Uses `https://services.leadconnectorhq.com` (from `apApiDomain` config)
- **Search operations**: Uses `https://backend.leadconnectorhq.com` (hardcoded for search endpoint)

This is necessary because the custom fields search endpoint is only available on the backend domain.

### Response Conversion

The search endpoint returns a different response structure than the standard endpoint. To ensure compatibility:

1. **Backend Search Response**: Contains additional properties like `_id`, `parentId`, `documentType`, etc.
2. **Conversion Function**: `convertBackendFieldToAPType()` maps the backend response to `APGetCustomFieldType`
3. **Field Mapping**:
   - `id` field: Prefers `id`, falls back to `_id`
   - `isAllowedCustomOption`: Maps from both `isAllowedCustomOption` and `allowCustomOption`
   - Extended properties: Preserved as optional fields in `APGetCustomFieldType`

This ensures that fields returned by `allWithParentFilter()` have the exact same structure and type compatibility as those returned by the existing `all()` method.

### Multiple Parent IDs

When multiple parent IDs are configured, the implementation:

1. Makes separate API calls for each parent ID
2. Combines all results into a single array
3. Handles errors gracefully (continues with other parents if one fails)
4. Provides detailed logging for each parent ID

### Error Handling

- Individual parent ID failures don't stop the entire process
- Detailed error logging with request ID correlation
- Graceful fallback behavior

### Performance Considerations

- Uses intelligent caching (same as existing methods)
- Configurable request limits (default: 1000 fields per parent)
- Efficient field deduplication if needed

## Logging

The implementation provides comprehensive logging:

```typescript
// Debug logs for configuration detection
logDebug("No parent filtering configured, fetching all custom fields", {
  locationId,
  parentFiltersCount: 0,
});

// Debug logs for each parent ID
logDebug("Fetching custom fields for parent", {
  locationId,
  parentId,
});

// Summary logs
logDebug("Completed parent-filtered custom fields fetch", {
  locationId,
  totalParents: apCustomFieldsParents.length,
  totalFieldsFound: allFilteredFields.length,
});
```

## Testing

A comprehensive test script is available at `New/test-parent-filtering.ts` that:

1. Tests current configuration behavior
2. Compares with traditional `all()` method
3. Analyzes field distribution by parent ID
4. Verifies filtering logic
5. Validates parent ID matching

Run the test with:

```bash
npx ts-node New/test-parent-filtering.ts
```

## Migration Guide

### For Existing Deployments

1. **No Action Required**: Default configuration maintains current behavior
2. **To Enable Filtering**: Update `apCustomFieldsParents` with desired parent IDs
3. **To Verify**: Run the test script to confirm expected behavior

### Configuration Update

```typescript
// Before (implicit)
apCustomFieldsParents: [] // Fetches all fields

// After (explicit filtering)
apCustomFieldsParents: ["RgQQpmZ5XV9dDm4hUsEX"] // Fetches only specified parent's fields
```

## Troubleshooting

### Common Issues

1. **No fields returned**: Verify parent IDs are correct and exist
2. **Unexpected field count**: Check if parent IDs contain the expected fields
3. **Performance issues**: Consider reducing the number of parent IDs or field limits

### Debug Steps

1. Enable debug logging to see detailed API calls
2. Use the test script to analyze field distribution
3. Verify parent IDs exist in the AutoPatient system
4. Check API response for error messages

## Security Considerations

- Parent IDs are validated through the AutoPatient API
- No additional authentication required (uses existing API key)
- Filtering happens server-side for security
- Request logging includes parent IDs for audit trails
